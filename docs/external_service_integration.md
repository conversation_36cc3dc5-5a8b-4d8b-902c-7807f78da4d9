# 外部训练服务集成文档

## 概述

本文档描述了训练任务管理服务与外部训练服务（hyai-train-biz）的集成实现。该集成允许：

1. 向外部服务发送训练任务状态变更通知
2. 接收来自外部服务的状态变更请求
3. 维护本地任务与外部任务的映射关系
4. 提供双向状态同步机制

## 架构组件

### 1. 配置管理 (`app/config/settings.py`)

```python
class ExternalServiceSettings(BaseSettings):
    base_url: str = "http://localhost:4003"
    status_change_endpoint: str = "/tasks/status-change"
    health_endpoint: str = "/train/health"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_notifications: bool = True
```

### 2. 外部服务客户端 (`app/services/external_client.py`)

负责与外部服务的HTTP通信：
- 发送状态变更通知
- 健康检查
- 重试机制和错误处理
- 状态映射

### 3. 任务映射存储 (`app/storage/external_mapping.py`)

管理本地任务ID与外部任务ID的映射关系：
- 创建和删除映射
- 双向查询（本地ID ↔ 外部ID）
- 同步时间跟踪

### 4. 通知服务 (`app/services/notification.py`)

协调状态变更通知：
- 集成训练服务和外部客户端
- 处理进度更新通知
- 管理任务映射生命周期

## API 接口

### 1. 注册外部任务映射

```http
POST /tasks/{task_id}/register-external?external_task_id={external_task_id}
```

**响应：**
```json
{
  "message": "外部任务映射注册成功",
  "local_task_id": 123,
  "external_task_id": 456
}
```

### 2. 获取外部任务映射

```http
GET /tasks/{task_id}/external-mapping
```

**响应：**
```json
{
  "local_task_id": 123,
  "external_task_id": 456,
  "has_mapping": true
}
```

### 3. 处理外部状态变更

```http
POST /tasks/status-change
Content-Type: application/json
```

**请求体：**
```json
{
  "external_task_id": 456,
  "status": "running",
  "progress": {
    "current_epoch": 10,
    "total_epochs": 100,
    "progress_percent": 10.0,
    "map50": 0.65,
    "map50_95": 0.45,
    "recall": 0.78,
    "precision": 0.82
  },
  "error_message": null,
  "output_model_path": null,
  "start_time": "2025-08-01T10:00:00",
  "end_time": null
}
```

**响应：**
```json
{
  "code": 0,
  "msg": "状态变更处理成功",
  "data": "状态变更处理成功"
}
```

### 4. 外部服务健康检查

```http
GET /external-service/health
```

**响应：**
```json
{
  "external_service_healthy": true,
  "status": "healthy",
  "message": "外部服务连接正常"
}
```

## 状态映射

### 本地状态 → 外部状态

| 本地状态 | 外部状态 | 描述 |
|---------|---------|------|
| PENDING | pending | 等待开始 |
| UPLOADING | uploading | 数据上传中 |
| RUNNING | running | 训练中 |
| PAUSED | paused | 已暂停 |
| STOPPING | stopping | 停止中 |
| STOPPED | cancelled | 已停止（映射为取消） |
| COMPLETED | completed | 已完成 |
| FAILED | failed | 失败 |
| CANCELLED | cancelled | 已取消 |

### 外部状态 → 本地状态

| 外部状态 | 本地状态 | 描述 |
|---------|---------|------|
| pending | PENDING | 等待开始 |
| uploading | UPLOADING | 数据上传中 |
| running | RUNNING | 训练中 |
| paused | PAUSED | 已暂停 |
| stopping | STOPPING | 停止中 |
| completed | COMPLETED | 已完成 |
| failed | FAILED | 失败 |
| cancelled | CANCELLED | 已取消 |

## 通知时机

### 自动发送通知的情况

1. **任务开始** - 状态变为 RUNNING
2. **任务完成** - 状态变为 COMPLETED
3. **任务失败** - 状态变为 FAILED
4. **任务停止** - 状态变为 STOPPED
5. **进度更新** - 每30秒发送一次（仅在RUNNING状态）

### 通知内容

- 任务状态变更
- 训练进度（轮次、百分比）
- 验证指标（mAP50、mAP50-95、召回率、精确度）
- 错误信息（如果有）
- 输出模型路径（完成时）
- 时间戳（开始时间、结束时间）

## 配置说明

### 环境变量

```bash
# 外部服务配置
EXTERNAL_SERVICE_BASE_URL=http://localhost:4003
EXTERNAL_SERVICE_STATUS_CHANGE_ENDPOINT=/tasks/status-change
EXTERNAL_SERVICE_HEALTH_ENDPOINT=/train/health
EXTERNAL_SERVICE_TIMEOUT=30
EXTERNAL_SERVICE_RETRY_ATTEMPTS=3
EXTERNAL_SERVICE_RETRY_DELAY=1.0
EXTERNAL_SERVICE_ENABLE_NOTIFICATIONS=true
```

### 禁用外部通知

如果需要禁用外部服务通知，设置：
```bash
EXTERNAL_SERVICE_ENABLE_NOTIFICATIONS=false
```

## 错误处理

### 重试机制

- 连接超时：自动重试，指数退避
- 请求错误：记录日志，继续执行
- 服务不可用：降级处理，不影响训练

### 日志记录

所有外部服务交互都会记录详细日志：
- 成功通知：INFO级别
- 重试尝试：WARNING级别
- 最终失败：ERROR级别

## 测试

### 运行集成测试

```bash
python test_external_integration.py
```

测试包括：
1. 服务健康检查
2. 任务创建和映射注册
3. 状态变更通知模拟
4. 双向状态同步验证

### 手动测试

1. 启动训练服务：`python -m uvicorn app.main:app --reload`
2. 创建训练任务
3. 注册外部任务映射
4. 发送状态变更请求
5. 验证状态同步

## 监控和运维

### 关键指标

- 外部服务连接成功率
- 状态变更通知延迟
- 重试次数和失败率
- 映射关系一致性

### 故障排查

1. **通知发送失败**
   - 检查外部服务是否可达
   - 验证网络连接和防火墙设置
   - 查看重试日志

2. **状态不同步**
   - 检查任务映射是否正确
   - 验证状态映射逻辑
   - 查看双方日志

3. **性能问题**
   - 调整超时和重试参数
   - 监控网络延迟
   - 考虑异步处理优化

## 安全考虑

1. **认证授权** - 建议添加API密钥或JWT认证
2. **网络安全** - 使用HTTPS协议
3. **输入验证** - 严格验证外部输入数据
4. **访问控制** - 限制状态变更接口的访问来源
