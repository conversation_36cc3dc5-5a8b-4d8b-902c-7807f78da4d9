# 训练任务外部服务集成使用说明

## 概述

本文档说明如何使用已集成的外部服务接口功能，实现训练任务状态和进度的实时同步。

## 集成功能

### 自动状态同步
- ✅ 训练开始时发送 `running` 状态
- ✅ **每个训练epoch完成后发送进度更新**
- ✅ 训练完成时发送 `completed` 状态和最终指标
- ✅ 训练失败时发送 `failed` 状态和错误信息
- ✅ 训练停止时发送 `cancelled` 状态

### 数据同步内容
- 训练进度（当前epoch、总epoch、进度百分比）
- 验证指标（mAP50、mAP50-95、精确率、召回率、损失）
- 时间戳（开始时间、结束时间）
- 错误信息和输出模型路径

## 使用步骤

### 1. 配置外部服务
在 `app/config/settings.py` 中配置外部服务地址：

```python
# 外部服务配置
external_service_settings = ExternalServiceSettings(
    base_url="http://localhost:4003",
    status_change_endpoint="/tasks/status-change",
    health_endpoint="/train/health",
    enable_notifications=True,  # 启用外部通知
    timeout=30.0,
    retry_attempts=3,
    retry_delay=1.0
)
```

### 2. 创建训练任务
```python
# 创建训练任务
task_request = CreateTaskRequest(
    task_id=123456,
    name="我的训练任务",
    config=TrainingConfig(
        model_name="yolov8n.pt",
        data_path="data/my_dataset.yaml",
        epochs=100,
        # ... 其他配置
    )
)

# POST /tasks
response = requests.post("http://localhost:8000/tasks", json=task_request.dict())
```

### 3. 确认外部任务ID
```python
# 外部任务ID就是本地任务ID，无需额外映射
external_task_id = task_id  # 123456

# 可选：调用API确认映射关系
response = requests.post(
    f"http://localhost:8000/tasks/{task_id}/register-external",
    params={"external_task_id": external_task_id}
)

print(response.json())
# 输出: {"message": "外部任务映射确认成功", "local_task_id": 123456, "external_task_id": 123456, "note": "本地任务ID即为外部任务ID，无需额外映射"}
```

### 4. 自动状态同步
训练过程中会自动发送以下状态更新（使用本地任务ID作为外部任务ID）：

#### 训练开始
```json
POST http://localhost:4003/tasks/status-change
{
    "external_task_id": 123456,
    "status": "running",
    "start_time": "2025-08-04T14:30:00"
}
```

#### 每个Epoch完成（自动发送）
```json
POST http://localhost:4003/tasks/status-change
{
    "external_task_id": 123456,
    "status": "running",
    "progress": {
        "current_epoch": 10,
        "total_epochs": 100,
        "progress_percent": 10.0,
        "map50": 0.65,
        "map50_95": 0.45,
        "recall": 0.72,
        "precision": 0.78,
        "loss": 0.85
    }
}
```

#### 训练完成
```json
POST http://localhost:4003/tasks/status-change
{
    "external_task_id": 123456,
    "status": "completed",
    "progress": {
        "current_epoch": 100,
        "total_epochs": 100,
        "progress_percent": 100.0,
        "map50": 0.85,
        "map50_95": 0.65,
        "recall": 0.82,
        "precision": 0.88
    },
    "output_model_path": "runs/train/exp/weights/best.pt",
    "end_time": "2025-08-04T16:45:00"
}
```

## API接口

### 确认外部任务映射
```http
POST /tasks/{task_id}/register-external?external_task_id={task_id}
```
注意：外部任务ID必须与本地任务ID相同

### 获取外部任务映射
```http
GET /tasks/{task_id}/external-mapping
```
返回本地任务ID作为外部任务ID

### 接收外部状态变更（被动接收）
```http
POST /tasks/status-change
```

## 状态映射

| 外部状态 | 本地状态 | 说明 |
|---------|---------|------|
| pending | PENDING | 等待开始 |
| uploading | UPLOADING | 数据上传中 |
| running | RUNNING | 训练中 |
| paused | PAUSED | 已暂停 |
| stopping | STOPPING | 停止中 |
| completed | COMPLETED | 已完成 |
| failed | FAILED | 失败 |
| cancelled | STOPPED/CANCELLED | 已取消 |

## 配置选项

### 外部服务设置
```python
class ExternalServiceSettings(BaseSettings):
    base_url: str = "http://localhost:4003"
    status_change_endpoint: str = "/tasks/status-change"
    health_endpoint: str = "/train/health"
    enable_notifications: bool = True  # 是否启用外部通知
    timeout: float = 30.0  # 请求超时时间
    retry_attempts: int = 3  # 重试次数
    retry_delay: float = 1.0  # 重试延迟
```

### 关键特性
- **自动重试机制**: 请求失败时自动重试，支持指数退避
- **健康检查**: 定期检查外部服务可用性
- **错误处理**: 外部服务不可用时不影响训练任务继续执行
- **状态映射**: 自动将本地状态映射为外部服务期望的状态格式

## 故障排除

### 外部服务不可用
- 系统会自动重试，不影响训练任务执行
- 检查外部服务地址和端口配置
- 查看日志中的连接错误信息

### 状态更新失败
- 检查外部任务ID映射是否正确
- 验证外部服务接口是否正常响应
- 查看重试日志和错误信息

### 进度更新频率
- 每个epoch完成后立即发送一次
- 可通过修改 `_update_training_progress` 中的逻辑调整频率

## 测试

运行演示脚本测试集成功能：
```bash
python test_external_integration_demo.py
```

该脚本会模拟完整的训练生命周期，展示所有状态变更通知的发送过程。