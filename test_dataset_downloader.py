#!/usr/bin/env python3
"""
数据集下载器功能测试

这个脚本测试数据集下载器的各项功能，包括：
1. 下载zip文件
2. 验证hash值
3. 解压文件
4. 缓存机制
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
sys.path.append(str(Path(__file__).parent))

from app.utils.dataset_downloader import dataset_downloader

async def test_dataset_downloader():
    """测试数据集下载器功能"""
    
    print("🧪 开始测试数据集下载器功能")
    print("=" * 60)
    
    # 测试配置
    # 使用一个小的测试文件来验证功能
    test_url = "https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip"
    # 注意：这里使用的是示例hash值，实际使用时需要提供正确的hash
    test_hash = "2cf0b8d2e8c70c5af14b4b4a8b0c8c1b"  
    
    print(f"📋 测试配置:")
    print(f"   URL: {test_url}")
    print(f"   Hash: {test_hash}")
    print(f"   缓存目录: {dataset_downloader.cache_dir}")
    print()
    
    try:
        # 测试1: 检查缓存状态
        print("🔍 测试1: 检查缓存状态")
        is_cached = dataset_downloader.is_dataset_cached(test_hash)
        print(f"   数据集是否已缓存: {is_cached}")
        
        if is_cached:
            cached_path = dataset_downloader.get_dataset_path(test_hash)
            print(f"   缓存路径: {cached_path}")
            print("   ℹ️ 数据集已缓存，将跳过下载")
        
        print()
        
        # 测试2: 下载和解压数据集
        print("🚀 测试2: 下载和解压数据集")
        print("   开始处理数据集...")
        
        start_time = asyncio.get_event_loop().time()
        
        # 注意: 由于这是测试，hash值可能不匹配，所以可能会下载失败
        # 在实际使用中，需要提供正确的hash值
        dataset_path = await dataset_downloader.download_and_extract_dataset(
            download_url=test_url,
            dataset_hash=test_hash
        )
        
        end_time = asyncio.get_event_loop().time()
        elapsed_time = end_time - start_time
        
        if dataset_path:
            print(f"   ✅ 处理成功!")
            print(f"   📁 数据集路径: {dataset_path}")
            print(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
            
            # 检查解压结果
            dataset_dir = Path(dataset_path)
            if dataset_dir.exists():
                files = list(dataset_dir.rglob("*"))
                print(f"   📊 解压文件数量: {len(files)}")
                print(f"   📋 前5个文件:")
                for i, file_path in enumerate(files[:5]):
                    if file_path.is_file():
                        file_size = file_path.stat().st_size
                        print(f"      {i+1}. {file_path.name} ({file_size} bytes)")
        else:
            print(f"   ❌ 处理失败")
            print(f"   ⚠️ 这可能是因为hash值不匹配或网络问题")
        
        print()
        
        # 测试3: 再次检查缓存（测试缓存机制）
        print("🔍 测试3: 验证缓存机制")
        print("   再次检查数据集缓存状态...")
        
        is_cached_after = dataset_downloader.is_dataset_cached(test_hash)
        print(f"   数据集现在是否已缓存: {is_cached_after}")
        
        if is_cached_after:
            cached_path = dataset_downloader.get_dataset_path(test_hash)
            print(f"   缓存路径: {cached_path}")
            print("   ✅ 缓存机制工作正常")
        
        print()
        
        # 测试4: 显示缓存目录信息
        print("📁 测试4: 缓存目录信息")
        cache_dir = dataset_downloader.cache_dir
        if cache_dir.exists():
            cache_items = list(cache_dir.iterdir())
            print(f"   缓存目录: {cache_dir}")
            print(f"   缓存项数量: {len(cache_items)}")
            
            for item in cache_items:
                if item.is_file():
                    size = item.stat().st_size / (1024 * 1024)  # MB
                    print(f"      📄 {item.name} ({size:.2f} MB)")
                elif item.is_dir():
                    file_count = len(list(item.rglob("*")))
                    print(f"      📁 {item.name}/ ({file_count} 文件)")
        else:
            print("   缓存目录不存在")
        
        print()
        print("🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_with_small_file():
    """使用小文件测试基本功能"""
    print("\n🧪 使用小文件测试基本功能")
    print("=" * 60)
    
    # 使用一个小的测试文件
    # 这里可以使用任何公开可访问的小zip文件
    test_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-zip-file.zip"
    # 为测试文件生成一个假的hash值
    import hashlib
    test_hash = hashlib.md5(test_url.encode()).hexdigest()
    
    print(f"📋 测试配置:")
    print(f"   URL: {test_url}")
    print(f"   Hash: {test_hash}")
    print()
    
    try:
        # 由于我们使用了假的hash值，下载可能会因为hash验证失败而失败
        # 这是正常的，展示了hash验证功能的工作
        print("⚠️ 注意: 这个测试使用假的hash值，用于演示hash验证功能")
        
        dataset_path = await dataset_downloader.download_and_extract_dataset(
            download_url=test_url,
            dataset_hash=test_hash
        )
        
        if dataset_path:
            print(f"✅ 意外成功！数据集路径: {dataset_path}")
        else:
            print("❌ 下载失败（预期结果，因为hash验证失败）")
            print("✅ Hash验证功能正常工作")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

def main():
    """主函数"""
    print("🤖 数据集下载器功能测试")
    print("=" * 80)
    
    # 检查缓存目录
    cache_dir = dataset_downloader.cache_dir
    print(f"📁 缓存目录: {cache_dir.absolute()}")
    
    if not cache_dir.exists():
        cache_dir.mkdir(parents=True)
        print("   ✅ 缓存目录已创建")
    else:
        print("   ✅ 缓存目录已存在")
    
    print()
    
    # 运行测试
    try:
        # 运行主要测试
        asyncio.run(test_dataset_downloader())
        
        # 运行小文件测试
        asyncio.run(test_with_small_file())
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 测试结束")

if __name__ == "__main__":
    main()