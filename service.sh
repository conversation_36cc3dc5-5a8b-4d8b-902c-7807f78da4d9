#!/bin/bash

# 训练服务管理脚本
# 支持 start、stop、restart、status 操作

# 配置
SERVICE_NAME="train_manager"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$PROJECT_DIR/logs/service.pid"
LOG_FILE="$PROJECT_DIR/logs/service.log"
HOST="0.0.0.0"
PORT="8000"
PYTHON_MODULE="app.main:app"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 确保日志目录存在
mkdir -p "$PROJECT_DIR/logs"

# 获取服务PID
get_service_pid() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            # 检查是否是我们的服务进程
            if ps -p "$pid" -o cmd= | grep -q "$PYTHON_MODULE"; then
                echo "$pid"
                return 0
            fi
        fi
        # PID文件存在但进程不存在，清理PID文件
        rm -f "$PID_FILE"
    fi
    return 1
}

# 检查服务是否运行
is_service_running() {
    get_service_pid > /dev/null
    return $?
}

# 启动服务
start_service() {
    echo -e "${BLUE}📋 $SERVICE_NAME 服务管理器${NC}"
    echo "=================================================="
    
    if is_service_running; then
        local pid=$(get_service_pid)
        echo -e "${YELLOW}⚠️ 服务已在运行中 (PID: $pid)${NC}"
        return 1
    fi
    
    echo -e "${GREEN}🚀 启动 $SERVICE_NAME 服务...${NC}"
    
    # 记录启动日志
    {
        echo ""
        echo "=================================================="
        echo "服务启动时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "启动命令: python -m uvicorn $PYTHON_MODULE --host $HOST --port $PORT"
        echo "=================================================="
    } >> "$LOG_FILE"
    
    # 启动服务
    cd "$PROJECT_DIR"
    nohup python -m uvicorn "$PYTHON_MODULE" \
        --host "$HOST" \
        --port "$PORT" \
        --log-level info \
        >> "$LOG_FILE" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待服务启动
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    sleep 3
    
    # 检查服务是否成功启动
    if is_service_running; then
        echo -e "${GREEN}✅ 服务启动成功!${NC}"
        echo "   PID: $pid"
        echo "   地址: http://$HOST:$PORT"
        echo "   API文档: http://$HOST:$PORT/docs"
        echo "   日志文件: $LOG_FILE"
        return 0
    else
        echo -e "${RED}❌ 服务启动失败，请检查日志文件${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}📋 $SERVICE_NAME 服务管理器${NC}"
    echo "=================================================="
    
    local pid=$(get_service_pid)
    if [ -z "$pid" ]; then
        echo -e "${YELLOW}⚠️ 服务未运行${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}🛑 停止 $SERVICE_NAME 服务 (PID: $pid)...${NC}"
    
    # 优雅关闭
    echo -e "${YELLOW}⏳ 发送SIGTERM信号...${NC}"
    kill -TERM "$pid"
    
    # 等待进程结束
    local count=0
    while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if kill -0 "$pid" 2>/dev/null; then
        echo -e "${YELLOW}⚠️ 优雅停止超时，强制终止...${NC}"
        kill -KILL "$pid"
        sleep 2
        echo -e "${GREEN}✅ 服务已强制停止${NC}"
    else
        echo -e "${GREEN}✅ 服务已优雅停止${NC}"
    fi
    
    rm -f "$PID_FILE"
    
    # 记录停止日志
    echo "服务停止时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$LOG_FILE"
    
    return 0
}

# 重启服务
restart_service() {
    echo -e "${BLUE}📋 $SERVICE_NAME 服务管理器${NC}"
    echo "=================================================="
    echo -e "${BLUE}🔄 重启 $SERVICE_NAME 服务...${NC}"
    
    # 先停止服务
    local pid=$(get_service_pid)
    if [ -n "$pid" ]; then
        echo -e "${YELLOW}🛑 停止当前服务 (PID: $pid)...${NC}"
        kill -TERM "$pid"
        
        # 等待进程结束
        local count=0
        while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        if kill -0 "$pid" 2>/dev/null; then
            kill -KILL "$pid"
        fi
        
        rm -f "$PID_FILE"
    fi
    
    # 等待一下确保完全停止
    sleep 2
    
    # 启动服务
    start_service
}

# 获取服务状态
get_service_status() {
    echo -e "${BLUE}📋 $SERVICE_NAME 服务状态${NC}"
    echo "=================================================="
    
    local pid=$(get_service_pid)
    if [ -z "$pid" ]; then
        echo -e "${RED}❌ 服务状态: 未运行${NC}"
        return 1
    fi
    
    # 获取进程信息
    local start_time=$(ps -o lstart= -p "$pid" 2>/dev/null | xargs)
    local memory=$(ps -o rss= -p "$pid" 2>/dev/null | xargs)
    local cpu=$(ps -o %cpu= -p "$pid" 2>/dev/null | xargs)
    
    if [ -n "$memory" ]; then
        memory=$((memory / 1024)) # 转换为MB
    fi
    
    echo -e "${GREEN}✅ 服务状态: 运行中${NC}"
    echo "   PID: $pid"
    echo "   启动时间: $start_time"
    echo "   内存使用: ${memory} MB"
    echo "   CPU使用: ${cpu}%"
    echo "   访问地址: http://$HOST:$PORT"
    echo "   API文档: http://$HOST:$PORT/docs"
    echo "   日志文件: $LOG_FILE"
    
    return 0
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}📋 $SERVICE_NAME 服务管理器${NC}"
    echo "=================================================="
    echo "使用方法: $0 {start|stop|restart|status|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 stop     # 停止服务"
    echo "  $0 restart  # 重启服务"
    echo "  $0 status   # 查看状态"
}

# 主函数
main() {
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            get_service_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}❌ 无效的命令: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$1"
