#!/usr/bin/env python3
"""
测试数据集下载和路径解析修复
"""
import os
import asyncio
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.dataset_downloader import dataset_downloader

async def test_dataset_extraction():
    """测试数据集解压和路径解析"""

    # 测试hash值（已知存在的数据集）
    test_hash = "fbe8cb0d8d20c0c3277db1c005c9bf0a"

    print("🧪 开始测试数据集路径解析...")

    # 1. 检查数据集是否已缓存
    if dataset_downloader.is_dataset_cached(test_hash):
        print(f"✅ 数据集已缓存: {test_hash}")

        # 获取数据集路径
        dataset_path = dataset_downloader.get_dataset_path(test_hash)
        print(f"📁 数据集路径: {dataset_path}")

        # 检查data.yaml文件
        data_yaml_path = os.path.join(dataset_path, "data.yaml")
        if os.path.exists(data_yaml_path):
            print(f"✅ 找到data.yaml文件: {data_yaml_path}")

            # 读取原始data.yaml内容
            print(f"📄 原始data.yaml内容:")
            with open(data_yaml_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            print(original_content)

            # 模拟训练服务的data.yaml更新逻辑
            try:
                # 读取YAML配置
                with open(data_yaml_path, 'r', encoding='utf-8') as f:
                    data_config = yaml.safe_load(f)

                print(f"📊 原始path字段: {data_config.get('path', 'N/A')}")

                # 更新path字段为数据集的绝对路径
                data_config['path'] = os.path.abspath(dataset_path)

                # 写回data.yaml文件
                with open(data_yaml_path, 'w', encoding='utf-8') as f:
                    yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)

                print(f"✅ 已更新data.yaml中的path字段: {data_config['path']}")

                # 读取更新后的内容
                print(f"📄 更新后data.yaml内容:")
                with open(data_yaml_path, 'r', encoding='utf-8') as f:
                    updated_content = f.read()
                print(updated_content)

            except Exception as yaml_error:
                print(f"❌ 更新data.yaml文件失败: {yaml_error}")

        else:
            print(f"❌ 未找到data.yaml文件: {data_yaml_path}")

        # 检查目录结构（只显示主要目录）
        print(f"\n📂 数据集目录结构:")
        for root, dirs, files in os.walk(dataset_path):
            level = root.replace(dataset_path, '').count(os.sep)
            if level > 2:  # 限制显示深度
                continue
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            if level < 2:  # 只在前两层显示文件
                subindent = ' ' * 2 * (level + 1)
                for file in files[:3]:  # 只显示前3个文件
                    print(f"{subindent}{file}")
                if len(files) > 3:
                    print(f"{subindent}... 还有 {len(files) - 3} 个文件")

    else:
        print(f"❌ 数据集未缓存: {test_hash}")

    print("\n🎯 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_dataset_extraction())
