#!/usr/bin/env python3
"""
检查当前运行的训练进程
"""

import psutil
import time


def check_training_processes():
    """检查当前运行的训练相关进程"""
    print("🔍 检查当前运行的训练相关进程...")

    training_processes = []
    training_keywords = [
        'yolo', 'ultralytics', 'train', 'model.train',
        '_run_training_in_process', 'ProcessPoolExecutor',
        'yolov8', 'yolov5', 'detectron', 'mmdetection'
    ]

    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cpu_percent', 'ppid']):
        try:
            proc_info = proc.info
            if not proc_info['name']:
                continue

            name_lower = proc_info['name'].lower()
            cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
            cmdline_lower = cmdline.lower()

            is_training_process = False
            process_type = ""

            # 1. Python进程且包含训练关键词
            if 'python' in name_lower:
                if any(keyword in cmdline_lower for keyword in training_keywords):
                    is_training_process = True
                    process_type = "Python训练进程"

            # 2. 直接匹配进程名
            elif any(keyword in name_lower for keyword in ['yolo', 'train']):
                is_training_process = True
                process_type = "训练进程"

            # 3. 检查是否是训练管理器的子进程
            elif proc_info['ppid'] and 'python' in name_lower:
                try:
                    parent = psutil.Process(proc_info['ppid'])
                    parent_cmdline = ' '.join(parent.cmdline()).lower()
                    if 'train_manger' in parent_cmdline or 'uvicorn' in parent_cmdline:
                        is_training_process = True
                        process_type = "训练子进程"
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            if is_training_process:
                training_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'cmdline': cmdline,
                    'create_time': proc_info['create_time'],
                    'cpu_percent': proc.cpu_percent(),
                    'ppid': proc_info['ppid'],
                    'type': process_type
                })

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if training_processes:
        print(f"📊 发现 {len(training_processes)} 个训练相关进程:")
        for i, proc in enumerate(training_processes, 1):
            create_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(proc['create_time']))
            print(f"\n{i}. {proc['type']}:")
            print(f"   PID: {proc['pid']}")
            print(f"   父PID: {proc['ppid']}")
            print(f"   名称: {proc['name']}")
            print(f"   创建时间: {create_time}")
            print(f"   CPU使用率: {proc['cpu_percent']:.1f}%")
            print(f"   命令行: {proc['cmdline'][:150]}...")
    else:
        print("✅ 未发现运行中的训练进程")
    
    return training_processes


def kill_training_processes():
    """终止所有训练相关进程"""
    print("\n⚠️  准备终止所有训练相关进程...")
    
    training_processes = check_training_processes()
    
    if not training_processes:
        print("✅ 没有需要终止的进程")
        return
    
    killed_count = 0
    for proc_info in training_processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            print(f"🔪 终止进程 PID: {proc_info['pid']}")
            proc.terminate()  # 先尝试优雅终止
            try:
                proc.wait(timeout=3)  # 等待3秒
                print(f"✅ 进程 {proc_info['pid']} 已优雅终止")
            except psutil.TimeoutExpired:
                proc.kill()  # 强制终止
                print(f"💀 进程 {proc_info['pid']} 已强制终止")
            killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"❌ 无法终止进程 {proc_info['pid']}: {e}")
    
    print(f"\n📈 总共终止了 {killed_count} 个进程")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "kill":
        kill_training_processes()
    else:
        check_training_processes()
        print("\n💡 提示: 使用 'python check_training_processes.py kill' 来终止所有训练进程")
