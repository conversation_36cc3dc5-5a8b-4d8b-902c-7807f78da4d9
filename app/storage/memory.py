from typing import Dict
from app.models.schemas import TrainingTask


class MemoryStorage:
    def __init__(self):
        self._storage: Dict[int, TrainingTask] = {}
    
    def get(self, task_id: int) -> TrainingTask:
        return self._storage.get(task_id)

    def get_all(self) -> list[TrainingTask]:
        return list(self._storage.values())

    def save(self, task: TrainingTask) -> None:
        self._storage[task.task_id] = task

    def exists(self, task_id: int) -> bool:
        return task_id in self._storage

    def delete(self, task_id: int) -> bool:
        if task_id in self._storage:
            del self._storage[task_id]
            return True
        return False


task_storage = MemoryStorage()