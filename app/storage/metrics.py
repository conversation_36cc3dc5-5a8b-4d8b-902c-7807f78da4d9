from typing import Dict, List
from datetime import datetime
from app.models.schemas import MetricsHistory, LogEntry


class MetricsStorage:
    def __init__(self):
        self._metrics: Dict[int, MetricsHistory] = {}
        self._logs: Dict[int, List[LogEntry]] = {}
    
    def save_metrics(self, task_id: int, epoch: int, metrics: dict) -> None:
        if task_id not in self._metrics:
            self._metrics[task_id] = MetricsHistory(task_id=task_id)
        
        metric_entry = {
            "epoch": epoch,
            "timestamp": datetime.now().isoformat(),
            **metrics
        }
        self._metrics[task_id].history.append(metric_entry)
    
    def get_metrics(self, task_id: int) -> MetricsHistory:
        return self._metrics.get(task_id, MetricsHistory(task_id=task_id))
    
    def add_log(self, task_id: int, level: str, message: str, epoch: int = None) -> None:
        if task_id not in self._logs:
            self._logs[task_id] = []
        
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=level,
            message=message,
            epoch=epoch
        )
        self._logs[task_id].append(log_entry)
    
    def get_logs(self, task_id: int, limit: int = 100) -> List[LogEntry]:
        logs = self._logs.get(task_id, [])
        return logs[-limit:] if limit else logs
    
    def clear_task_data(self, task_id: int) -> None:
        self._metrics.pop(task_id, None)
        self._logs.pop(task_id, None)


metrics_storage = MetricsStorage()