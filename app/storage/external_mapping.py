"""
外部任务映射存储
管理本地任务ID与外部任务ID的映射关系
"""

from typing import Dict, Optional
from datetime import datetime
from app.models.schemas import ExternalTaskMapping


class ExternalMappingStorage:
    """外部任务映射存储"""
    
    def __init__(self):
        # local_task_id -> ExternalTaskMapping
        self._mappings: Dict[int, ExternalTaskMapping] = {}
        # external_task_id -> local_task_id (反向索引)
        self._reverse_mappings: Dict[int, int] = {}
    
    def create_mapping(self, local_task_id: int, external_task_id: int) -> ExternalTaskMapping:
        """
        创建任务映射
        
        Args:
            local_task_id: 本地任务ID
            external_task_id: 外部任务ID
            
        Returns:
            ExternalTaskMapping: 创建的映射对象
        """
        mapping = ExternalTaskMapping(
            local_task_id=local_task_id,
            external_task_id=external_task_id,
            created_at=datetime.now()
        )
        
        self._mappings[local_task_id] = mapping
        self._reverse_mappings[external_task_id] = local_task_id
        
        return mapping
    
    def get_external_task_id(self, local_task_id: int) -> Optional[int]:
        """
        根据本地任务ID获取外部任务ID
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            Optional[int]: 外部任务ID，不存在时返回None
        """
        mapping = self._mappings.get(local_task_id)
        return mapping.external_task_id if mapping else None
    
    def get_local_task_id(self, external_task_id: int) -> Optional[int]:
        """
        根据外部任务ID获取本地任务ID
        
        Args:
            external_task_id: 外部任务ID
            
        Returns:
            Optional[int]: 本地任务ID，不存在时返回None
        """
        return self._reverse_mappings.get(external_task_id)
    
    def get_mapping(self, local_task_id: int) -> Optional[ExternalTaskMapping]:
        """
        获取任务映射
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            Optional[ExternalTaskMapping]: 映射对象，不存在时返回None
        """
        return self._mappings.get(local_task_id)
    
    def update_sync_time(self, local_task_id: int) -> bool:
        """
        更新最后同步时间
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            bool: 更新是否成功
        """
        mapping = self._mappings.get(local_task_id)
        if mapping:
            mapping.last_sync_at = datetime.now()
            return True
        return False
    
    def remove_mapping(self, local_task_id: int) -> bool:
        """
        删除任务映射
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            bool: 删除是否成功
        """
        mapping = self._mappings.get(local_task_id)
        if mapping:
            del self._mappings[local_task_id]
            del self._reverse_mappings[mapping.external_task_id]
            return True
        return False
    
    def exists(self, local_task_id: int) -> bool:
        """
        检查映射是否存在
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            bool: 映射是否存在
        """
        return local_task_id in self._mappings
    
    def get_all_mappings(self) -> Dict[int, ExternalTaskMapping]:
        """
        获取所有映射
        
        Returns:
            Dict[int, ExternalTaskMapping]: 所有映射，key为本地任务ID
        """
        return self._mappings.copy()
    
    def clear(self) -> None:
        """清空所有映射"""
        self._mappings.clear()
        self._reverse_mappings.clear()


# 全局映射存储实例
external_mapping_storage = ExternalMappingStorage()
