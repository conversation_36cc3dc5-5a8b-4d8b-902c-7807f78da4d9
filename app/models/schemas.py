from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from .enums import TaskStatus


class TrainingConfig(BaseModel):
    model_name: str = "yolov8n.pt"
    data_path: str
    dataset_download_url: Optional[str] = None  # 数据集下载地址
    dataset_hash: Optional[str] = None          # 数据集文件hash值
    epochs: int = 100
    imgsz: int = 640
    batch_size: int = 16
    learning_rate: float 
    device: str = "auto"
    project: str = "runs/train"
    name: Optional[str] = None
    save_period: int
    patience: int
    cache: bool = True
    workers: int
    optimizer: str 
    weight_decay: float 
    momentum: float
    additional_params: Dict[str, Any] = {}


class ValidationMetrics(BaseModel):
    precision: float = 0.0
    recall: float = 0.0
    map50: float = 0.0
    map50_95: float = 0.0
    f1_score: float = 0.0


class ResourceUsage(BaseModel):
    gpu_memory_used: float = 0.0  # MB
    gpu_memory_total: float = 0.0  # MB
    gpu_utilization: float = 0.0  # %
    cpu_percent: float = 0.0
    memory_percent: float = 0.0


class TrainingProgress(BaseModel):
    epoch: int = 0
    total_epochs: int = 0
    progress_percent: float = 0.0
    train_loss: float = 0.0
    val_loss: float = 0.0
    learning_rate: float = 0.0
    eta: Optional[str] = None  # estimated time of arrival


class Checkpoint(BaseModel):
    epoch: int
    file_path: str
    metrics: ValidationMetrics
    created_at: datetime
    file_size: int  # bytes


class TrainingTask(BaseModel):
    task_id: int
    name: str
    config: TrainingConfig
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    paused_at: Optional[datetime] = None
    
    # 训练进度
    progress: TrainingProgress = TrainingProgress()
    
    # 验证指标
    current_metrics: ValidationMetrics = ValidationMetrics()
    best_metrics: ValidationMetrics = ValidationMetrics()
    
    # 资源使用
    resource_usage: ResourceUsage = ResourceUsage()
    
    # 错误信息
    error_message: Optional[str] = None
    warnings: List[str] = []
    
    # 输出路径
    output_path: Optional[str] = None
    log_file: Optional[str] = None
    
    # 日志面板
    dashboard_url: Optional[str] = None
    
    # 通知服务
    notification_host: Optional[str] = None
    
    # 检查点
    checkpoints: List[Checkpoint] = []
    best_checkpoint: Optional[str] = None


class CreateTaskRequest(BaseModel):
    name: str
    task_id: int
    config: TrainingConfig
    notification_host: Optional[str] = None  # 通知服务host地址，用于拼接进度通知请求


class TaskControlRequest(BaseModel):
    reason: Optional[str] = None


class MetricsHistory(BaseModel):
    task_id: int
    history: List[Dict[str, Any]] = []
    
    
class LogEntry(BaseModel):
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR
    message: str
    epoch: Optional[int] = None


# 外部服务状态变更相关模型
# 外部服务专用的DTO模型（匹配外部服务期望的字段结构）

class TrainingProgressDTO(BaseModel):
    """训练进度DTO - 匹配外部服务格式"""
    epoch: Optional[int] = Field(None, description="当前轮次")
    total_epochs: Optional[int] = Field(None, description="总轮次") 
    progress_percent: Optional[float] = Field(None, description="进度百分比")
    train_loss: Optional[float] = Field(None, description="训练损失")
    val_loss: Optional[float] = Field(None, description="验证损失")
    learning_rate: Optional[float] = Field(None, description="学习率")
    eta: Optional[str] = Field(None, description="预计剩余时间")

class ValidationMetricsDTO(BaseModel):
    """验证指标DTO - 匹配外部服务格式"""
    precision: Optional[float] = Field(None, description="精确率")
    recall: Optional[float] = Field(None, description="召回率")
    map50: Optional[float] = Field(None, description="mAP@0.5")
    map50_95: Optional[float] = Field(None, description="mAP@0.5:0.95")
    f1_score: Optional[float] = Field(None, description="F1分数")

class ResourceUsageDTO(BaseModel):
    """资源使用DTO - 匹配外部服务格式"""
    gpu_memory_used: Optional[float] = Field(None, description="GPU内存使用量(MB)")
    gpu_memory_total: Optional[float] = Field(None, description="GPU内存总量(MB)")
    gpu_utilization: Optional[float] = Field(None, description="GPU利用率(%)")
    cpu_percent: Optional[float] = Field(None, description="CPU使用率(%)")
    memory_percent: Optional[float] = Field(None, description="内存使用率(%)")

class CheckpointDTO(BaseModel):
    """检查点DTO - 匹配外部服务格式"""
    epoch: Optional[int] = Field(None, description="轮次")
    file_path: Optional[str] = Field(None, description="文件路径")
    metrics: Optional[ValidationMetricsDTO] = Field(None, description="检查点指标")
    created_at: Optional[str] = Field(None, description="创建时间")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")

class FileTransferMethod(BaseModel):
    """文件传输方式"""
    method: str = Field(..., description="传输方式: http_upload, ftp, copy, base64")
    endpoint: Optional[str] = Field(None, description="上传端点URL")
    credentials: Optional[Dict[str, str]] = Field(None, description="认证信息")
    target_path: Optional[str] = Field(None, description="目标路径")

class ModelDeliveryDTO(BaseModel):
    """模型交付信息DTO"""
    model_path: str = Field(..., description="训练完成的模型文件路径")
    model_format: str = Field(default="pytorch", description="模型格式")
    model_size: Optional[int] = Field(None, description="模型文件大小(字节)")
    model_version: Optional[str] = Field(None, description="模型版本")
    training_config: Optional[Dict[str, Any]] = Field(None, description="训练配置")
    performance_metrics: Optional[ValidationMetricsDTO] = Field(None, description="模型性能指标")
    file_transfer: Optional[FileTransferMethod] = Field(None, description="文件传输方式")
    file_content_base64: Optional[str] = Field(None, description="文件内容(Base64编码)")
    file_hash: Optional[str] = Field(None, description="文件哈希值(用于验证)")
    transfer_status: Optional[str] = Field(default="pending", description="传输状态")
    
class TaskStatusChangeRequestDTO(BaseModel):
    """任务状态变更请求DTO - 发送给外部服务（匹配外部服务期望格式）"""
    external_task_id: int = Field(..., description="外部任务ID")
    status: str = Field(..., description="任务状态")
    progress: Optional[TrainingProgressDTO] = Field(None, description="训练进度信息")
    current_metrics: Optional[ValidationMetricsDTO] = Field(None, description="当前训练指标")
    best_metrics: Optional[ValidationMetricsDTO] = Field(None, description="最佳训练指标")
    resource_usage: Optional[ResourceUsageDTO] = Field(None, description="资源使用情况")
    error_message: Optional[str] = Field(None, description="错误信息")
    warnings: Optional[List[str]] = Field(None, description="警告信息列表")
    output_path: Optional[str] = Field(None, description="模型输出路径")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    checkpoints: Optional[List[CheckpointDTO]] = Field(None, description="检查点列表")
    best_checkpoint: Optional[str] = Field(None, description="最佳检查点路径")
    model_delivery: Optional[ModelDeliveryDTO] = Field(None, description="模型交付信息")
    timestamp: Optional[str] = Field(None, description="状态变更时间")

    class Config:
        # 使用snake_case命名策略
        alias_generator = lambda field_name: field_name
        populate_by_name = True

# 保持向后兼容的别名
TrainingProgressInfo = TrainingProgressDTO  # 向后兼容


class TaskStatusChangeResponseDTO(BaseModel):
    """任务状态变更响应DTO - 从外部服务接收"""
    code: int = Field(..., description="响应代码，0表示成功，1表示失败")
    msg: Optional[str] = Field(None, description="响应消息")
    data: Optional[str] = Field(None, description="响应数据")


class ExternalTaskMapping(BaseModel):
    """外部任务映射"""
    local_task_id: int = Field(..., description="本地任务ID")
    external_task_id: int = Field(..., description="外部任务ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    last_sync_at: Optional[datetime] = Field(None, description="最后同步时间")