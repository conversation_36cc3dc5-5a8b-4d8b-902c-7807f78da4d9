import os
import hashlib
import zipfile
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import Optional, <PERSON>ple
from datetime import datetime


class DatasetDownloader:
    """数据集下载器，支持基于hash值的缓存和自动解压"""
    
    def __init__(self, cache_dir: str = "datasets_cache"):
        """
        初始化数据集下载器
        
        Args:
            cache_dir: 缓存目录路径，默认为 'datasets_cache'
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
    def _get_file_hash(self, file_path: Path) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _get_cache_paths(self, dataset_hash: str) -> Tu<PERSON>[Path, Path]:
        """
        根据hash值生成缓存路径
        
        Args:
            dataset_hash: 数据集文件的hash值
            
        Returns:
            Tuple[Path, Path]: (zip文件路径, 解压目录路径)
        """
        zip_file_path = self.cache_dir / f"{dataset_hash}.zip"
        extract_dir_path = self.cache_dir / dataset_hash
        return zip_file_path, extract_dir_path
    
    def is_dataset_cached(self, dataset_hash: str) -> bool:
        """
        检查数据集是否已经缓存
        
        Args:
            dataset_hash: 数据集的hash值
            
        Returns:
            bool: 如果数据集已缓存且解压目录存在返回True，否则返回False
        """
        _, extract_dir = self._get_cache_paths(dataset_hash)
        return extract_dir.exists() and extract_dir.is_dir()
    
    async def download_file(self, url: str, file_path: Path, 
                          expected_hash: Optional[str] = None) -> bool:
        """
        异步下载文件
        
        Args:
            url: 下载地址
            file_path: 保存文件路径
            expected_hash: 期望的文件hash值，用于验证
            
        Returns:
            bool: 下载成功返回True，否则返回False
        """
        try:
            print(f"📥 开始下载文件: {url}")
            print(f"📁 保存路径: {file_path}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        print(f"❌ 下载失败，HTTP状态码: {response.status}")
                        return False
                    
                    # 获取文件大小
                    file_size = int(response.headers.get('content-length', 0))
                    downloaded = 0
                    
                    # 创建目录
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 写入文件
                    async with aiofiles.open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                            downloaded += len(chunk)
                            
                            # 显示进度
                            if file_size > 0:
                                progress = (downloaded / file_size) * 100
                                print(f"\r📊 下载进度: {progress:.1f}% ({downloaded}/{file_size} bytes)", end='')
                    
                    print(f"\n✅ 文件下载完成: {file_path}")
                    
                    # 验证hash值
                    if expected_hash:
                        actual_hash = self._get_file_hash(file_path)
                        if actual_hash != expected_hash:
                            print(f"❌ Hash值验证失败:")
                            print(f"   期望: {expected_hash}")
                            print(f"   实际: {actual_hash}")
                            # 删除错误的文件
                            file_path.unlink()
                            return False
                        print(f"✅ Hash值验证通过: {expected_hash}")
                    
                    return True
                    
        except Exception as e:
            print(f"❌ 下载文件时发生异常: {str(e)}")
            # 清理部分下载的文件
            if file_path.exists():
                try:
                    file_path.unlink()
                except:
                    pass
            return False
    
    def extract_zip(self, zip_file: Path, extract_dir: Path) -> bool:
        """
        解压zip文件

        Args:
            zip_file: zip文件路径
            extract_dir: 解压目录路径

        Returns:
            bool: 解压成功返回True，否则返回False
        """
        try:
            print(f"📦 开始解压文件: {zip_file}")
            print(f"📁 解压到目录: {extract_dir}")

            # 创建解压目录
            extract_dir.mkdir(parents=True, exist_ok=True)

            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                # 获取所有文件信息
                file_list = zip_ref.namelist()
                total_files = len(file_list)
                print(f"📋 压缩包包含 {total_files} 个文件")

                # 解压所有文件
                for i, file_info in enumerate(file_list, 1):
                    # 修复Windows路径分隔符问题：将反斜杠转换为正斜杠
                    normalized_path = file_info.replace('\\', '/')

                    # 如果路径被修改了，需要手动处理解压
                    if normalized_path != file_info:
                        # 读取文件内容
                        file_data = zip_ref.read(file_info)

                        # 创建目标文件路径
                        target_path = extract_dir / normalized_path

                        # 创建目录结构
                        target_path.parent.mkdir(parents=True, exist_ok=True)

                        # 写入文件
                        with open(target_path, 'wb') as f:
                            f.write(file_data)
                    else:
                        # 使用标准解压方法
                        zip_ref.extract(file_info, extract_dir)

                    if i % 100 == 0 or i == total_files:
                        progress = (i / total_files) * 100
                        print(f"\r📊 解压进度: {progress:.1f}% ({i}/{total_files} 文件)", end='')

                print(f"\n✅ 解压完成，共解压 {total_files} 个文件")
                return True

        except zipfile.BadZipFile:
            print(f"❌ 文件不是有效的zip格式: {zip_file}")
            return False
        except Exception as e:
            print(f"❌ 解压文件时发生异常: {str(e)}")
            return False
    
    async def download_and_extract_dataset(self, download_url: str, 
                                         dataset_hash: str) -> Optional[str]:
        """
        下载并解压数据集（带缓存）
        
        Args:
            download_url: 数据集下载地址
            dataset_hash: 数据集文件的hash值
            
        Returns:
            Optional[str]: 成功返回解压后的数据集目录路径，失败返回None
        """
        try:
            # 获取缓存路径
            zip_file_path, extract_dir = self._get_cache_paths(dataset_hash)
            
            # 检查是否已经缓存
            if self.is_dataset_cached(dataset_hash):
                print(f"✅ 数据集已缓存，直接使用: {extract_dir}")
                return str(extract_dir)
            
            print(f"🚀 开始处理数据集: hash={dataset_hash}")
            
            # 检查zip文件是否已下载
            need_download = True
            if zip_file_path.exists():
                # 验证已下载文件的hash
                actual_hash = self._get_file_hash(zip_file_path)
                if actual_hash == dataset_hash:
                    print(f"✅ Zip文件已存在且hash验证通过: {zip_file_path}")
                    need_download = False
                else:
                    print(f"⚠️ Zip文件hash不匹配，重新下载")
                    print(f"   文件hash: {actual_hash}")
                    print(f"   期望hash: {dataset_hash}")
                    zip_file_path.unlink()  # 删除错误的文件
            
            # 下载文件（如果需要）
            if need_download:
                success = await self.download_file(download_url, zip_file_path, dataset_hash)
                if not success:
                    print(f"❌ 数据集下载失败")
                    return None
            
            # 检查解压目录是否存在
            if extract_dir.exists():
                print(f"⚠️ 解压目录已存在，清理后重新解压: {extract_dir}")
                import shutil
                shutil.rmtree(extract_dir)
            
            # 解压文件
            success = self.extract_zip(zip_file_path, extract_dir)
            if not success:
                print(f"❌ 数据集解压失败")
                return None
            
            print(f"🎉 数据集处理完成: {extract_dir}")
            return str(extract_dir)
            
        except Exception as e:
            print(f"❌ 处理数据集时发生异常: {str(e)}")
            return None
    
    def get_dataset_path(self, dataset_hash: str) -> Optional[str]:
        """
        获取数据集路径（仅查询，不下载）
        
        Args:
            dataset_hash: 数据集hash值
            
        Returns:
            Optional[str]: 如果数据集已缓存返回路径，否则返回None
        """
        if self.is_dataset_cached(dataset_hash):
            _, extract_dir = self._get_cache_paths(dataset_hash)
            return str(extract_dir)
        return None
    
    def cleanup_cache(self, max_age_days: int = 30):
        """
        清理缓存中的旧文件
        
        Args:
            max_age_days: 最大保存天数，超过此时间的文件将被删除
        """
        try:
            current_time = datetime.now()
            cleaned_count = 0
            
            for item in self.cache_dir.iterdir():
                # 计算文件/目录的年龄
                item_time = datetime.fromtimestamp(item.stat().st_mtime)
                age_days = (current_time - item_time).days
                
                if age_days > max_age_days:
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        import shutil
                        shutil.rmtree(item)
                    cleaned_count += 1
                    print(f"🗑️ 清理过期缓存: {item} (年龄: {age_days} 天)")
            
            if cleaned_count > 0:
                print(f"✅ 缓存清理完成，清理了 {cleaned_count} 项")
            else:
                print(f"✅ 无需清理，所有缓存文件都在有效期内")
                
        except Exception as e:
            print(f"❌ 清理缓存时发生异常: {str(e)}")


# 创建全局实例
dataset_downloader = DatasetDownloader()