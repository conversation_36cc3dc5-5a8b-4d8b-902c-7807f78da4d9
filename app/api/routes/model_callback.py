"""
模型回调接口
管理训练完成后的模型交付回调
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.services.model_callback import model_callback_service

router = APIRouter(prefix="/model-callback", tags=["model-callback"])


class CallbackEndpointRequest(BaseModel):
    """回调端点请求"""
    endpoint: str


class CallbackEndpointResponse(BaseModel):
    """回调端点响应"""
    endpoints: List[str]
    enabled: bool
    message: str


class ModelDeliveryRequest(BaseModel):
    """手动模型交付请求"""
    task_id: int
    model_path: str
    transfer_method: Optional[str] = "http_upload"
    upload_endpoint: Optional[str] = None
    target_path: Optional[str] = None


@router.post("/endpoints", response_model=CallbackEndpointResponse)
async def register_callback_endpoint(request: CallbackEndpointRequest):
    """
    注册模型回调端点
    """
    success = await model_callback_service.register_callback_endpoint(request.endpoint)
    
    if success:
        return CallbackEndpointResponse(
            endpoints=model_callback_service.get_callback_endpoints(),
            enabled=model_callback_service.is_enabled(),
            message=f"回调端点已注册: {request.endpoint}"
        )
    else:
        raise HTTPException(status_code=400, detail="注册回调端点失败")


@router.delete("/endpoints", response_model=CallbackEndpointResponse)
async def unregister_callback_endpoint(request: CallbackEndpointRequest):
    """
    取消注册模型回调端点
    """
    success = await model_callback_service.unregister_callback_endpoint(request.endpoint)
    
    if success:
        return CallbackEndpointResponse(
            endpoints=model_callback_service.get_callback_endpoints(),
            enabled=model_callback_service.is_enabled(),
            message=f"回调端点已取消注册: {request.endpoint}"
        )
    else:
        raise HTTPException(status_code=400, detail="取消注册回调端点失败")


@router.get("/endpoints", response_model=CallbackEndpointResponse)
async def get_callback_endpoints():
    """
    获取所有注册的回调端点
    """
    return CallbackEndpointResponse(
        endpoints=model_callback_service.get_callback_endpoints(),
        enabled=model_callback_service.is_enabled(),
        message="获取回调端点列表成功"
    )


@router.post("/enable")
async def enable_callback_service():
    """
    启用模型回调服务
    """
    model_callback_service.enable()
    return {
        "message": "模型回调服务已启用",
        "enabled": model_callback_service.is_enabled()
    }


@router.post("/disable")
async def disable_callback_service():
    """
    禁用模型回调服务
    """
    model_callback_service.disable()
    return {
        "message": "模型回调服务已禁用",
        "enabled": model_callback_service.is_enabled()
    }


@router.get("/status")
async def get_callback_service_status():
    """
    获取模型回调服务状态
    """
    return {
        "enabled": model_callback_service.is_enabled(),
        "endpoints_count": len(model_callback_service.get_callback_endpoints()),
        "endpoints": model_callback_service.get_callback_endpoints()
    }


@router.post("/deliver")
async def manual_model_delivery(request: ModelDeliveryRequest):
    """
    手动触发模型交付（用于测试或重新交付）
    """
    try:
        from app.storage.memory import task_storage
        from app.models.schemas import ValidationMetricsDTO
        
        # 获取任务信息
        task = task_storage.get(request.task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"任务 {request.task_id} 不存在")
        
        # 构建性能指标
        performance_metrics = ValidationMetricsDTO(
            precision=task.best_metrics.precision,
            recall=task.best_metrics.recall,
            map50=task.best_metrics.map50,
            map50_95=task.best_metrics.map50_95,
            f1_score=task.best_metrics.f1_score
        )
        
        # 执行模型交付
        success = await model_callback_service.deliver_trained_model(
            task_id=request.task_id,
            model_path=request.model_path,
            performance_metrics=performance_metrics,
            transfer_method=request.transfer_method,
            upload_endpoint=request.upload_endpoint,
            target_path=request.target_path
        )
        
        if success:
            return {
                "message": f"模型交付成功: {request.model_path}",
                "task_id": request.task_id,
                "model_path": request.model_path,
                "success": True
            }
        else:
            raise HTTPException(status_code=500, detail="模型交付失败")
            
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(status_code=500, detail=f"模型交付异常: {str(e)}")


@router.get("/transfer-methods")
async def get_supported_transfer_methods():
    """
    获取支持的文件传输方式
    """
    return {
        "supported_methods": model_callback_service.supported_methods,
        "default_method": model_callback_service.default_transfer_method,
        "max_file_size": model_callback_service.max_file_size,
        "descriptions": {
            "base64": "将文件编码为Base64字符串，适合小文件",
            "http_upload": "通过HTTP POST上传文件到指定端点",
            "copy": "直接复制文件到目标路径，适合本地传输",
            "ftp": "通过FTP协议上传文件（需要额外配置）"
        }
    }


@router.post("/configure")
async def configure_callback_service(
    default_transfer_method: Optional[str] = None,
    max_file_size: Optional[int] = None
):
    """
    配置模型回调服务
    """
    result = {}
    
    if default_transfer_method:
        if default_transfer_method in model_callback_service.supported_methods:
            model_callback_service.default_transfer_method = default_transfer_method
            result["default_transfer_method"] = default_transfer_method
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的传输方式: {default_transfer_method}"
            )
    
    if max_file_size:
        if max_file_size > 0:
            model_callback_service.max_file_size = max_file_size
            result["max_file_size"] = max_file_size
        else:
            raise HTTPException(status_code=400, detail="文件大小限制必须大于0")
    
    return {
        "message": "配置更新成功",
        "current_config": {
            "default_transfer_method": model_callback_service.default_transfer_method,
            "max_file_size": model_callback_service.max_file_size,
            "supported_methods": model_callback_service.supported_methods
        },
        "updated": result
    }


@router.get("/transfer-progress/{task_id}")
async def get_transfer_progress(task_id: int):
    """
    获取文件传输进度
    """
    progress = model_callback_service.get_transfer_progress(task_id)
    
    if progress:
        return {
            "task_id": task_id,
            "progress": progress
        }
    else:
        raise HTTPException(status_code=404, detail=f"未找到任务 {task_id} 的传输进度")


@router.delete("/transfer-progress/{task_id}")
async def clear_transfer_progress(task_id: int):
    """
    清理文件传输进度记录
    """
    model_callback_service.clear_transfer_progress(task_id)
    return {
        "message": f"已清理任务 {task_id} 的传输进度记录"
    }


@router.get("/transfer-progress")
async def get_all_transfer_progress():
    """
    获取所有文件传输进度
    """
    return {
        "progress_records": model_callback_service.transfer_progress,
        "total_records": len(model_callback_service.transfer_progress)
    }