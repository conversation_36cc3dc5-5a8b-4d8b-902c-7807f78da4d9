from fastapi import APIRouter, HTTPException
from typing import List
from datetime import datetime

from app.models.schemas import (
    TrainingTask, CreateTaskRequest,
    TaskStatusChangeRequestDTO, TaskStatusChangeResponseDTO
)
from app.models.enums import TaskStatus
from app.storage.memory import task_storage
from app.services.training import start_training
from app.services.notification import notification_service

router = APIRouter(prefix="/tasks", tags=["tasks"])


@router.get("", response_model=List[TrainingTask])
async def get_tasks():
    return task_storage.get_all()


@router.get("/{task_id}", response_model=TrainingTask)
async def get_task(task_id: int):
    task = task_storage.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务未找到")
    return task


@router.post("", response_model=TrainingTask)
async def create_task(request: CreateTaskRequest):
    task = TrainingTask(
        task_id=request.task_id,
        name=request.name,
        config=request.config,
        status=TaskStatus.PENDING,
        created_at=datetime.now(),
        notification_host=request.notification_host  # 保存通知host
    )
    task_storage.save(task)
    
    # 直接启动后台训练任务（异步非阻塞）
    import asyncio
    asyncio.create_task(start_training(request.task_id))
    
    return task


@router.delete("/{task_id}")
async def delete_task(task_id: int):
    """删除训练任务"""
    if not task_storage.exists(task_id):
        raise HTTPException(status_code=404, detail="任务未找到")
    
    task = task_storage.get(task_id)
    if task.status in [TaskStatus.RUNNING, TaskStatus.PENDING, TaskStatus.PAUSED]:
        raise HTTPException(status_code=400, detail="无法删除正在运行的任务，请先停止任务")
    
    # 删除任务数据
    task_storage.delete(task_id)
    
    # 清理相关的指标和日志数据
    from app.storage.metrics import metrics_storage
    metrics_storage.clear_task_data(task_id)

    # 清理外部任务映射
    await notification_service.unregister_external_task(task_id)

    return {"message": "任务已删除"}


@router.post("/{task_id}/register-external")
async def register_external_task(task_id: int, external_task_id: int):
    """注册外部任务映射（已简化：本地任务ID即外部任务ID）"""
    if not task_storage.exists(task_id):
        raise HTTPException(status_code=404, detail="任务未找到")

    # 验证外部任务ID与本地任务ID是否一致
    if task_id != external_task_id:
        raise HTTPException(
            status_code=400, 
            detail=f"外部任务ID必须与本地任务ID一致: local_id={task_id}, external_id={external_task_id}"
        )

    return {
        "message": "外部任务映射确认成功",
        "local_task_id": task_id,
        "external_task_id": external_task_id,
        "note": "本地任务ID即为外部任务ID，无需额外映射"
    }


@router.get("/{task_id}/external-mapping")
async def get_external_mapping(task_id: int):
    """获取外部任务映射（已简化：本地任务ID即外部任务ID）"""
    if not task_storage.exists(task_id):
        raise HTTPException(status_code=404, detail="任务未找到")

    return {
        "local_task_id": task_id,
        "external_task_id": task_id,  # 本地任务ID即外部任务ID
        "has_mapping": True,
        "note": "本地任务ID即为外部任务ID"
    }


@router.post("/status-change", response_model=TaskStatusChangeResponseDTO)
async def handle_status_change(request: TaskStatusChangeRequestDTO):
    """
    处理外部服务的状态变更请求
    这个接口用于接收来自外部服务的状态变更通知
    """
    try:
        # 直接使用外部任务ID作为本地任务ID
        local_task_id = request.external_task_id
        
        task = task_storage.get(local_task_id)
        if not task:
            return TaskStatusChangeResponseDTO(
                code=1,
                msg=f"本地任务 {local_task_id} 不存在",
                data=None
            )

        # 映射外部状态到本地状态
        status_mapping = {
            "pending": TaskStatus.PENDING,
            "uploading": TaskStatus.UPLOADING,
            "running": TaskStatus.RUNNING,
            "paused": TaskStatus.PAUSED,
            "stopping": TaskStatus.STOPPING,
            "completed": TaskStatus.COMPLETED,
            "failed": TaskStatus.FAILED,
            "cancelled": TaskStatus.CANCELLED
        }

        new_status = status_mapping.get(request.status, TaskStatus.PENDING)
        previous_status = task.status

        # 更新任务状态
        task.status = new_status

        # 更新进度信息
        if request.progress:
            if request.progress.current_epoch is not None:
                task.progress.epoch = request.progress.current_epoch
            if request.progress.total_epochs is not None:
                task.progress.total_epochs = request.progress.total_epochs
            if request.progress.progress_percent is not None:
                task.progress.progress_percent = request.progress.progress_percent

            # 更新指标
            if request.progress.map50 is not None:
                task.current_metrics.map50 = request.progress.map50
            if request.progress.map50_95 is not None:
                task.current_metrics.map50_95 = request.progress.map50_95
            if request.progress.recall is not None:
                task.current_metrics.recall = request.progress.recall
            if request.progress.precision is not None:
                task.current_metrics.precision = request.progress.precision

        # 更新错误信息
        if request.error_message:
            task.error_message = request.error_message

        # 更新输出路径
        if request.output_model_path:
            task.output_path = request.output_model_path

        # 更新时间戳
        if request.start_time and not task.started_at:
            from datetime import datetime
            task.started_at = datetime.fromisoformat(request.start_time.replace('Z', '+00:00'))

        if request.end_time and not task.completed_at:
            from datetime import datetime
            task.completed_at = datetime.fromisoformat(request.end_time.replace('Z', '+00:00'))

        # 根据状态自动设置时间戳
        if new_status == TaskStatus.RUNNING and not task.started_at:
            task.started_at = datetime.now()
        elif new_status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and not task.completed_at:
            task.completed_at = datetime.now()

        # 记录日志
        from app.storage.metrics import metrics_storage
        metrics_storage.add_log(
            local_task_id,
            "INFO",
            f"外部状态变更: {previous_status.value} -> {new_status.value}"
        )

        return TaskStatusChangeResponseDTO(
            code=0,
            msg="状态变更处理成功",
            data="状态变更处理成功"
        )

    except Exception as e:
        return TaskStatusChangeResponseDTO(
            code=1,
            msg=f"状态变更处理失败: {str(e)}",
            data=None
        )