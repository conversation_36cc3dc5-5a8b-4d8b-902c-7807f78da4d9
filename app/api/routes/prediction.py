"""
模型预测API接口
提供模型预测、批量预测、模型管理等功能
"""

import io
import base64
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, model_validator

from app.services.model_prediction import model_prediction_service

router = APIRouter(prefix="/prediction", tags=["prediction"])


class PredictionRequest(BaseModel):
    """预测请求"""
    model_path: Optional[str] = None  # 本地模型路径（与model_url二选一）
    model_url: Optional[str] = None   # 模型下载地址（与model_path二选一）
    model_hash: Optional[str] = None  # 模型hash值，用于缓存
    image_base64: str
    confidence: Optional[float] = None
    iou_threshold: Optional[float] = None
    image_size: Optional[int] = None
    save_visualization: bool = True
    
    @model_validator(mode='after')
    def validate_model_params(self):
        """验证模型参数：必须提供model_path或model_url中的一个"""
        if not self.model_path and not self.model_url:
            raise ValueError('必须提供model_path或model_url中的一个')
        if self.model_path and self.model_url:
            raise ValueError('model_path和model_url只能提供其中一个')
        return self


class BatchPredictionRequest(BaseModel):
    """批量预测请求"""
    model_path: Optional[str] = None  # 本地模型路径（与model_url二选一）
    model_url: Optional[str] = None   # 模型下载地址（与model_path二选一）
    model_hash: Optional[str] = None  # 模型hash值，用于缓存
    images_base64: List[str]
    confidence: Optional[float] = None
    iou_threshold: Optional[float] = None
    image_size: Optional[int] = None
    
    @model_validator(mode='after')
    def validate_model_params(self):
        """验证模型参数：必须提供model_path或model_url中的一个"""
        if not self.model_path and not self.model_url:
            raise ValueError('必须提供model_path或model_url中的一个')
        if self.model_path and self.model_url:
            raise ValueError('model_path和model_url只能提供其中一个')
        return self


class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    model_path: str
    file_size: int
    created_time: str
    modified_time: str
    is_cached: bool


@router.post("/predict")
async def predict_image(request: PredictionRequest):
    """
    单张图片预测
    """
    try:
        # 解码Base64图片
        try:
            image_data = base64.b64decode(request.image_base64)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Base64图片解码失败: {str(e)}")
        
        # 执行预测
        result = await model_prediction_service.predict_image(
            model_path=request.model_path,
            model_url=request.model_url,
            model_hash=request.model_hash,
            image_data=image_data,
            confidence=request.confidence,
            iou_threshold=request.iou_threshold,
            image_size=request.image_size,
            save_visualization=request.save_visualization
        )
        
        if not result['success']:
            raise HTTPException(status_code=500, detail=result.get('error', '预测失败'))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测异常: {str(e)}")


@router.post("/predict-file")
async def predict_image_file(
    model_path: str = Form(...),
    confidence: Optional[float] = Form(None),
    iou_threshold: Optional[float] = Form(None),
    image_size: Optional[int] = Form(None),
    save_visualization: bool = Form(True),
    image: UploadFile = File(...)
):
    """
    上传图片文件进行预测
    """
    try:
        # 验证文件类型
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传有效的图片文件")
        
        # 读取图片数据
        image_data = await image.read()
        
        # 执行预测
        result = await model_prediction_service.predict_image(
            model_path=model_path,
            image_data=image_data,
            confidence=confidence,
            iou_threshold=iou_threshold,
            image_size=image_size,
            save_visualization=save_visualization
        )
        
        if not result['success']:
            raise HTTPException(status_code=500, detail=result.get('error', '预测失败'))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测异常: {str(e)}")


@router.post("/batch-predict")
async def batch_predict_images(request: BatchPredictionRequest):
    """
    批量图片预测
    """
    try:
        if len(request.images_base64) > 20:  # 限制批量预测数量
            raise HTTPException(status_code=400, detail="批量预测最多支持20张图片")
        
        # 解码所有Base64图片
        images_data = []
        for i, img_base64 in enumerate(request.images_base64):
            try:
                images_data.append(base64.b64decode(img_base64))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"第{i+1}张图片Base64解码失败: {str(e)}")
        
        # 执行批量预测
        results = await model_prediction_service.batch_predict(
            model_path=request.model_path,
            model_url=request.model_url,
            model_hash=request.model_hash,
            images_data=images_data,
            confidence=request.confidence,
            iou_threshold=request.iou_threshold,
            image_size=request.image_size
        )
        
        return {
            "success": True,
            "total_images": len(results),
            "results": results,
            "summary": {
                "successful_predictions": sum(1 for r in results if r['success']),
                "failed_predictions": sum(1 for r in results if not r['success']),
                "total_detections": sum(len(r.get('predictions', {}).get('detections', [])) for r in results if r['success'])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量预测异常: {str(e)}")


@router.post("/batch-predict-files")
async def batch_predict_image_files(
    model_path: str = Form(...),
    confidence: Optional[float] = Form(None),
    iou_threshold: Optional[float] = Form(None),
    image_size: Optional[int] = Form(None),
    images: List[UploadFile] = File(...)
):
    """
    上传多个图片文件进行批量预测
    """
    try:
        if len(images) > 20:
            raise HTTPException(status_code=400, detail="批量预测最多支持20张图片")
        
        # 读取所有图片数据
        images_data = []
        for i, image in enumerate(images):
            if not image.content_type or not image.content_type.startswith('image/'):
                raise HTTPException(status_code=400, detail=f"第{i+1}个文件不是有效的图片文件")
            
            images_data.append(await image.read())
        
        # 执行批量预测
        results = await model_prediction_service.batch_predict(
            model_path=model_path,
            images_data=images_data,
            confidence=confidence,
            iou_threshold=iou_threshold,
            image_size=image_size
        )
        
        return {
            "success": True,
            "total_images": len(results),
            "results": results,
            "summary": {
                "successful_predictions": sum(1 for r in results if r['success']),
                "failed_predictions": sum(1 for r in results if not r['success']),
                "total_detections": sum(len(r.get('predictions', {}).get('detections', [])) for r in results if r['success'])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量预测异常: {str(e)}")


@router.get("/model-info")
async def get_model_info(model_path: str):
    """
    获取模型信息
    """
    try:
        info = model_prediction_service.get_model_info(model_path)
        
        if 'error' in info:
            raise HTTPException(status_code=404, detail=info['error'])
        
        return info
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型信息失败: {str(e)}")


@router.delete("/model-cache")
async def clear_model_cache(model_path: Optional[str] = None):
    """
    清理模型缓存
    """
    try:
        model_prediction_service.clear_model_cache(model_path)
        
        if model_path:
            return {"message": f"已清理模型缓存: {model_path}"}
        else:
            return {"message": "已清理所有模型缓存"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")


@router.get("/service-status")
async def get_service_status():
    """
    获取预测服务状态
    """
    try:
        status = model_prediction_service.get_service_status()
        return status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")


@router.post("/configure")
async def configure_service(
    confidence_threshold: Optional[float] = None,
    iou_threshold: Optional[float] = None,
    cache_limit: Optional[int] = None
):
    """
    配置预测服务参数
    """
    try:
        updated = {}
        
        if confidence_threshold is not None:
            if 0.0 <= confidence_threshold <= 1.0:
                model_prediction_service.confidence_threshold = confidence_threshold
                updated['confidence_threshold'] = confidence_threshold
            else:
                raise HTTPException(status_code=400, detail="置信度阈值必须在0.0-1.0之间")
        
        if iou_threshold is not None:
            if 0.0 <= iou_threshold <= 1.0:
                model_prediction_service.iou_threshold = iou_threshold
                updated['iou_threshold'] = iou_threshold
            else:
                raise HTTPException(status_code=400, detail="IoU阈值必须在0.0-1.0之间")
        
        if cache_limit is not None:
            if cache_limit > 0:
                model_prediction_service.model_cache_limit = cache_limit
                updated['cache_limit'] = cache_limit
            else:
                raise HTTPException(status_code=400, detail="缓存限制必须大于0")
        
        return {
            "message": "配置更新成功",
            "updated": updated,
            "current_config": model_prediction_service.get_service_status()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """
    获取支持的图片格式
    """
    return {
        "supported_formats": model_prediction_service.supported_formats,
        "max_batch_size": 20,
        "description": {
            ".jpg/.jpeg": "JPEG图片格式",
            ".png": "PNG图片格式",
            ".bmp": "位图格式",
            ".tiff": "TIFF格式",
            ".webp": "WebP格式"
        }
    }