from fastapi import APIRouter
from app.services.notification import notification_service

router = APIRouter()


@router.get("/")
async def root():
    return {"message": "训练任务管理服务"}


@router.get("/health")
async def health_check():
    return {"status": "healthy"}


@router.get("/external-service/health")
async def external_service_health():
    """检查外部服务健康状态"""
    is_healthy = await notification_service.check_service_health()
    return {
        "external_service_healthy": is_healthy,
        "status": "healthy" if is_healthy else "warning",
        "message": "外部服务连接正常" if is_healthy else "外部服务连接异常"
    }