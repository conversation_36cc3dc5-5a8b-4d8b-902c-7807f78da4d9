from fastapi import APIRouter, HTTPException
from datetime import datetime
from app.models.schemas import TaskControlRequest
from app.models.enums import TaskStatus
from app.storage.memory import task_storage
from app.storage.metrics import metrics_storage
from app.services.training import start_training, stop_training, force_kill_training_processes

router = APIRouter(prefix="/tasks/{task_id}", tags=["control"])


@router.post("/pause")
async def pause_task(task_id: int, request: TaskControlRequest = TaskControlRequest()):
    """暂停训练任务"""
    task = task_storage.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务未找到")
    
    if task.status != TaskStatus.RUNNING:
        raise HTTPException(status_code=400, detail="只能暂停正在运行的任务")
    
    task.status = TaskStatus.PAUSED
    task.paused_at = datetime.now()
    
    # 记录暂停日志
    reason = request.reason or "用户手动暂停"
    metrics_storage.add_log(task_id, "INFO", f"任务已暂停: {reason}")
    
    return {"message": "任务已暂停", "status": task.status}


@router.post("/resume")
async def resume_task(task_id: int, request: TaskControlRequest = TaskControlRequest()):
    """恢复训练任务"""
    task = task_storage.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务未找到")
    
    if task.status != TaskStatus.PAUSED:
        raise HTTPException(status_code=400, detail="只能恢复已暂停的任务")
    
    task.status = TaskStatus.PENDING
    task.paused_at = None
    
    # 记录恢复日志
    reason = request.reason or "用户手动恢复"
    metrics_storage.add_log(task_id, "INFO", f"任务已恢复: {reason}")
    
    # 重新启动训练（异步非阻塞）
    import asyncio
    asyncio.create_task(start_training(task_id))
    
    return {"message": "任务已恢复", "status": task.status}


@router.post("/restart")
async def restart_task(task_id: int, request: TaskControlRequest = TaskControlRequest()):
    """重启训练任务"""
    task = task_storage.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务未找到")
    
    if task.status in [TaskStatus.RUNNING, TaskStatus.PENDING]:
        raise HTTPException(status_code=400, detail="无法重启正在运行或等待中的任务")
    
    # 重置任务状态
    task.status = TaskStatus.PENDING
    task.started_at = None
    task.completed_at = None
    task.paused_at = None
    task.error_message = None
    task.warnings = []
    
    # 重置进度和指标
    task.progress = task.progress.__class__()
    task.current_metrics = task.current_metrics.__class__()
    task.resource_usage = task.resource_usage.__class__()
    
    # 记录重启日志
    reason = request.reason or "用户手动重启"
    metrics_storage.add_log(task_id, "INFO", f"任务已重启: {reason}")
    
    # 启动训练（异步非阻塞）
    import asyncio
    asyncio.create_task(start_training(task_id))
    
    return {"message": "任务已重启", "status": task.status}


@router.post("/stop")
async def stop_task(task_id: int, request: TaskControlRequest = TaskControlRequest()):
    """强制停止训练任务"""
    try:
        # 记录停止日志
        reason = request.reason or "用户手动停止"
        metrics_storage.add_log(task_id, "WARNING", f"任务正在停止: {reason}")
        
        # 调用实际的停止函数
        await stop_training(task_id)
        
        return {"message": "任务已停止", "status": TaskStatus.STOPPED}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止任务失败: {str(e)}")


@router.post("/force-stop")
async def force_stop_task(task_id: int, request: TaskControlRequest = TaskControlRequest()):
    """强制停止训练任务（终止所有相关进程）"""
    try:
        # 记录强制停止日志
        reason = request.reason or "用户强制停止"
        metrics_storage.add_log(task_id, "WARNING", f"任务正在强制停止: {reason}")

        # 先尝试正常停止
        try:
            await stop_training(task_id)
        except ValueError:
            # 如果任务不存在或状态不对，仍然执行强制终止
            pass

        # 强制终止所有训练进程
        await force_kill_training_processes()

        # 更新任务状态（如果任务存在）
        task = task_storage.get(task_id)
        if task:
            task.status = TaskStatus.STOPPED
            task.completed_at = datetime.now()
            metrics_storage.add_log(task_id, "WARNING", f"任务已被强制停止: {reason}")

        return {"message": "任务已强制停止，所有相关进程已终止", "status": TaskStatus.STOPPED}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"强制停止任务失败: {str(e)}")


@router.post("/emergency-stop")
async def emergency_stop_all():
    """紧急停止所有训练任务和进程"""
    try:
        print("🚨 执行紧急停止所有训练...")

        # 强制终止所有训练进程
        await force_kill_training_processes()

        # 清理所有任务状态
        from app.services.training import running_tasks, training_futures, training_processes

        # 获取所有正在运行的任务ID
        running_task_ids = list(running_tasks.keys()) + list(training_futures.keys()) + list(training_processes.keys())

        stopped_count = 0
        for task_id in set(running_task_ids):  # 去重
            try:
                task = task_storage.get(task_id)
                if task and task.status in [TaskStatus.RUNNING, TaskStatus.PENDING]:
                    task.status = TaskStatus.STOPPED
                    task.completed_at = datetime.now()
                    metrics_storage.add_log(task_id, "ERROR", "任务被紧急停止")
                    stopped_count += 1
            except Exception as e:
                print(f"更新任务 {task_id} 状态失败: {e}")

        # 清理所有全局状态
        running_tasks.clear()
        training_futures.clear()
        training_processes.clear()

        # 强制重置进程池
        from app.services.training import _force_shutdown_process_pool
        await _force_shutdown_process_pool()

        message = f"紧急停止完成，已停止 {stopped_count} 个任务，所有训练进程已终止"
        print(f"✅ {message}")

        return {"message": message, "stopped_tasks": stopped_count}

    except Exception as e:
        error_msg = f"紧急停止失败: {str(e)}"
        print(f"❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)