from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any

from app.services.log_dashboard import log_dashboard_service
from app.storage.memory import task_storage

router = APIRouter(prefix="/dashboard", tags=["日志面板"])


class DashboardStartRequest(BaseModel):
    """启动面板请求模型"""
    log_path: Optional[str] = None
    host: str = "127.0.0.1"
    port: Optional[int] = None


class DashboardResponse(BaseModel):
    """面板响应模型"""
    success: bool
    message: str
    url: Optional[str] = None
    port: Optional[int] = None
    log_path: Optional[str] = None
    error: Optional[str] = None


class DashboardStatusResponse(BaseModel):
    """面板状态响应模型"""
    is_running: bool
    host: Optional[str] = None
    port: Optional[int] = None
    url: Optional[str] = None
    log_path: Optional[str] = None


class ExperimentInfoResponse(BaseModel):
    """实验信息响应模型"""
    task_id: str
    experiment_id: Optional[str] = None
    experiment_name: Optional[str] = None
    dashboard_port: Optional[int] = None
    dashboard_url: Optional[str] = None
    log_path: Optional[str] = None
    found: bool


@router.post("/start", response_model=DashboardResponse)
async def start_dashboard(request: DashboardStartRequest):
    """启动日志面板服务"""
    try:
        result = await log_dashboard_service.start_dashboard(
            log_path=request.log_path,
            host=request.host,
            port=request.port
        )
        
        return DashboardResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"启动日志面板失败: {str(e)}"
        )


@router.post("/stop", response_model=DashboardResponse)
async def stop_dashboard():
    """停止日志面板服务"""
    try:
        result = await log_dashboard_service.stop_dashboard()
        return DashboardResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"停止日志面板失败: {str(e)}"
        )


@router.post("/restart", response_model=DashboardResponse)
async def restart_dashboard():
    """重启日志面板服务"""
    try:
        result = await log_dashboard_service.restart_dashboard()
        return DashboardResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"重启日志面板失败: {str(e)}"
        )


@router.get("/status", response_model=DashboardStatusResponse)
async def get_dashboard_status():
    """获取日志面板服务状态"""
    try:
        status = log_dashboard_service.get_status()
        return DashboardStatusResponse(**status)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取面板状态失败: {str(e)}"
        )


@router.get("/url")
async def get_dashboard_url():
    """获取日志面板访问地址"""
    status = log_dashboard_service.get_status()
    
    if not status["is_running"]:
        raise HTTPException(
            status_code=503,
            detail="日志面板服务未运行，请先启动服务"
        )
    
    return {
        "url": status["url"],
        "message": "请直接访问此URL查看日志面板"
    }


@router.get("/experiment/{task_id}", response_model=ExperimentInfoResponse)
async def get_experiment_info(task_id: str):
    """通过task_id查询SwanBoard实验信息"""
    try:
     
        # 获取面板状态
        dashboard_status = log_dashboard_service.get_status()
        
        if not dashboard_status["is_running"]:
            return ExperimentInfoResponse(
                task_id=task_id,
                found=False,
                experiment_id=None,
                experiment_name=None,
                dashboard_port=None,
                dashboard_url=None,
                log_path=dashboard_status["log_path"]
            )
        
        # 尝试查询SwanBoard数据库中的实验信息
        experiment_id = None
        experiment_name = None
        
        try:
            from swanboard.db import Experiment
            
            
            # 查询数据库中的实验
            try:
                # 查找匹配的实验
                experiments = Experiment.select().where(
                    (Experiment.task_id == task_id) 
                ).order_by(Experiment.create_time.desc())
                
                if experiments:
                    experiment = experiments[0]  # 获取最新的实验
                    experiment_id = str(experiment.id)
                    experiment_name = experiment.name
            except Exception as db_error:
                print(f"⚠️ 查询实验 {task_id} 时出错: {db_error}")
     
                    
        except ImportError:
            print("⚠️ 无法导入swanboard.db.Experiment，可能SwanBoard未正确安装")
        except Exception as e:
            print(f"⚠️ 查询SwanBoard实验信息时出错: {e}")
        
        return ExperimentInfoResponse(
            task_id=task_id,
            experiment_id=experiment_id,
            experiment_name=experiment_name,
            dashboard_port=dashboard_status["port"],
            dashboard_url=dashboard_status["url"],
            log_path=dashboard_status["log_path"],
            found=experiment_id is not None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询实验信息失败: {str(e)}"
        )


@router.get("/experiments")
async def list_all_experiments():
    """列出所有SwanBoard实验"""
    try:
        # 获取面板状态
        dashboard_status = log_dashboard_service.get_status()
        
        if not dashboard_status["is_running"]:
            raise HTTPException(
                status_code=503,
                detail="日志面板服务未运行"
            )
        
        experiments = []
        
        try:
            from swanboard.db import Experiment
            
            # 查询所有实验
            all_experiments = Experiment.select().order_by(Experiment.create_time.desc())
            
            for exp in all_experiments:
                # 尝试从实验名称中提取task_id
                exp_name = exp.name
                experiments.append({
                    "experiment_id": str(exp.id),
                    "experiment_name": exp_name,
                    "task_id": exp.task_id
                })
                
        except ImportError:
            print("⚠️ 无法导入swanboard.db.Experiment，可能SwanBoard未正确安装")
        except Exception as e:
            print(f"⚠️ 查询SwanBoard实验列表时出错: {e}")
        
        return {
            "success": True,
            "dashboard_port": dashboard_status["port"],
            "dashboard_url": dashboard_status["url"],
            "experiments_count": len(experiments),
            "experiments": experiments
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询实验列表失败: {str(e)}"
        )