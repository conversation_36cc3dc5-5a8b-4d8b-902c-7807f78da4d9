from fastapi import APIRouter
from app.models.schemas import ResourceUsage
from app.services.monitoring import resource_monitor

router = APIRouter(prefix="/system", tags=["system"])


@router.get("/resources", response_model=ResourceUsage)
async def get_system_resources():
    """获取当前系统资源使用情况"""
    return resource_monitor.get_current_usage()


@router.get("/health")
async def system_health():
    """获取系统健康状态"""
    usage = resource_monitor.get_current_usage()
    
    status = "healthy"
    issues = []
    
    # 检查资源使用情况
    if usage.cpu_percent > 90:
        issues.append("CPU使用率过高")
        status = "warning"
    
    if usage.memory_percent > 90:
        issues.append("内存使用率过高")
        status = "warning"
    
    if usage.gpu_memory_total > 0:
        gpu_usage_percent = (usage.gpu_memory_used / usage.gpu_memory_total) * 100
        if gpu_usage_percent > 90:
            issues.append("GPU内存使用率过高")
            status = "warning"
    
    if usage.gpu_utilization > 95:
        issues.append("GPU利用率过高")
        status = "warning"
    
    return {
        "status": status,
        "resource_usage": usage,
        "issues": issues
    }