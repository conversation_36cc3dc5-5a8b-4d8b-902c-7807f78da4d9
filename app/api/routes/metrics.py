from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from app.models.schemas import MetricsHistory, LogEntry
from app.storage.memory import task_storage
from app.storage.metrics import metrics_storage

router = APIRouter(prefix="/tasks/{task_id}", tags=["metrics"])


@router.get("/metrics", response_model=MetricsHistory)
async def get_task_metrics(task_id: int):
    """获取任务的训练指标历史"""
    if not task_storage.exists(task_id):
        raise HTTPException(status_code=404, detail="任务未找到")
    
    return metrics_storage.get_metrics(task_id)


@router.get("/logs", response_model=List[LogEntry])
async def get_task_logs(
    task_id: int,
    limit: Optional[int] = Query(100, description="日志条目数限制"),
    level: Optional[str] = Query(None, description="日志级别过滤 (INFO, WARNING, ERROR)")
):
    """获取任务的训练日志"""
    if not task_storage.exists(task_id):
        raise HTTPException(status_code=404, detail="任务未找到")
    
    logs = metrics_storage.get_logs(task_id, limit)
    
    if level:
        logs = [log for log in logs if log.level == level.upper()]
    
    return logs


@router.get("/checkpoints")
async def get_task_checkpoints(task_id: int):
    """获取任务的模型检查点列表"""
    task = task_storage.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务未找到")
    
    return {
        "task_id": task_id,
        "checkpoints": task.checkpoints,
        "best_checkpoint": task.best_checkpoint
    }