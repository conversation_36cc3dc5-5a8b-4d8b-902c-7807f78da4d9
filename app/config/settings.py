from pydantic_settings import BaseSettings


class SwanLabSettings(BaseSettings):
    enable_swanlab: bool = True
    logdir: str = "/home/<USER>/project/train_manger/logs"
    mode: str = "local"
    cuda_device_order: str = "PCI_BUS_ID"

    model_config = {"env_prefix": "SWANLAB_"}


class ExternalServiceSettings(BaseSettings):
    """外部训练服务配置"""
    base_url: str = "http://192.168.171.101:8080/api/train"
    status_change_endpoint: str = "/tasks/status-change"
    health_endpoint: str = "/train/health"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_notifications: bool = True

    model_config = {"env_prefix": "EXTERNAL_SERVICE_"}


swanlab_settings = SwanLabSettings()
external_service_settings = ExternalServiceSettings()