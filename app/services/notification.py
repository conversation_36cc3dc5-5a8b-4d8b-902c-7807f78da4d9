"""
训练任务状态变更通知服务
负责向外部服务发送状态变更通知
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from app.models.schemas import TrainingTask, TrainingProgressInfo
from app.models.enums import TaskStatus
from app.services.external_client import external_client
from app.config.settings import external_service_settings

logger = logging.getLogger(__name__)


class NotificationService:
    """训练任务状态变更通知服务"""
    
    def __init__(self):
        self.client = external_client
    
    async def notify_status_change(
        self, 
        task: TrainingTask, 
        previous_status: Optional[TaskStatus] = None
    ) -> bool:
        """
        通知任务状态变更
        
        Args:
            task: 训练任务对象
            previous_status: 之前的状态
            
        Returns:
            bool: 通知是否成功
        """
        try:
            # 直接使用本地任务ID作为外部任务ID
            external_task_id = task.task_id
            
            # 映射状态
            external_status = self.client.map_local_status_to_external(task.status)
            
            # 构建进度信息（使用新的DTO格式）
            from app.models.schemas import TrainingProgressDTO, ValidationMetricsDTO
            
            progress_info = None
            current_metrics = None
            
            if task.progress:
                progress_info = TrainingProgressDTO(
                    epoch=task.progress.epoch,  # 使用epoch而不是current_epoch
                    total_epochs=task.progress.total_epochs,
                    progress_percent=task.progress.progress_percent,
                    train_loss=task.progress.train_loss if task.progress.train_loss > 0 else None,
                    val_loss=task.progress.val_loss if task.progress.val_loss > 0 else None,
                    learning_rate=task.progress.learning_rate if task.progress.learning_rate > 0 else None
                )
            
            if task.current_metrics and any([task.current_metrics.map50, task.current_metrics.precision, task.current_metrics.recall]):
                current_metrics = ValidationMetricsDTO(
                    precision=task.current_metrics.precision if task.current_metrics.precision > 0 else None,
                    recall=task.current_metrics.recall if task.current_metrics.recall > 0 else None,
                    map50=task.current_metrics.map50 if task.current_metrics.map50 > 0 else None,
                    map50_95=task.current_metrics.map50_95 if task.current_metrics.map50_95 > 0 else None,
                    f1_score=task.current_metrics.f1_score if task.current_metrics.f1_score > 0 else None
                )
            
            # 如果任务有自定义的通知host，临时更新客户端配置
            original_host = None
            if task.notification_host:
                original_host = self.client.base_url
                self.client.update_host(task.notification_host)
                logger.info(f"使用任务特定的通知host: {task.notification_host}")
            
            try:
                # 发送通知（使用新的参数结构）
                timestamp = task.completed_at or task.started_at
                
                # 构建最佳指标信息（仅在完成状态时发送）
                best_metrics = None
                if (external_status == "completed" and 
                    hasattr(task, 'best_metrics') and 
                    any([task.best_metrics.map50, task.best_metrics.precision, task.best_metrics.recall])):
                    from app.models.schemas import ValidationMetricsDTO
                    best_metrics = ValidationMetricsDTO(
                        precision=task.best_metrics.precision if task.best_metrics.precision > 0 else None,
                        recall=task.best_metrics.recall if task.best_metrics.recall > 0 else None,
                        map50=task.best_metrics.map50 if task.best_metrics.map50 > 0 else None,
                        map50_95=task.best_metrics.map50_95 if task.best_metrics.map50_95 > 0 else None,
                        f1_score=task.best_metrics.f1_score if task.best_metrics.f1_score > 0 else None
                    )
                
                response = await self.client.send_status_change(
                    external_task_id=external_task_id,
                    status=external_status,
                    progress=progress_info,
                    current_metrics=current_metrics,
                    best_metrics=best_metrics,
                    error_message=task.error_message,
                    output_path=task.output_path,  # 使用output_path而不是output_model_path
                    timestamp=timestamp  # 使用timestamp而不是start_time/end_time
                )
            finally:
                # 恢复原始host配置
                if original_host:
                    self.client.base_url = original_host
                    self.client.status_change_url = f"{original_host}{external_service_settings.status_change_endpoint}"
                    self.client.health_url = f"{original_host}{external_service_settings.health_endpoint}"
            
            if response and response.code == 0:
                logger.info(f"任务状态变更通知发送成功: task_id={task.task_id}, "
                           f"external_task_id={external_task_id}, status={external_status}")
                return True
            else:
                logger.warning(f"任务状态变更通知发送失败: task_id={task.task_id}, "
                             f"external_task_id={external_task_id}, status={external_status}")
                return False
                
        except Exception as e:
            logger.error(f"发送状态变更通知时发生异常: task_id={task.task_id}, error={str(e)}")
            return False
    
    async def notify_progress_update(self, task: TrainingTask) -> bool:
        """
        通知训练进度更新
        
        Args:
            task: 训练任务对象
            
        Returns:
            bool: 通知是否成功
        """
        # 只有在运行状态时才发送进度更新
        if task.status != TaskStatus.RUNNING:
            return False
            
        return await self.notify_status_change(task)
    
    async def register_external_task(self, local_task_id: int, external_task_id: int) -> bool:
        """
        注册外部任务映射（已简化：本地任务ID即外部任务ID）
        
        Args:
            local_task_id: 本地任务ID
            external_task_id: 外部任务ID（应该与本地任务ID相同）
            
        Returns:
            bool: 注册是否成功
        """
        # 现在本地任务ID就是外部任务ID，无需映射存储
        if local_task_id == external_task_id:
            logger.info(f"外部任务映射确认: task_id={local_task_id}")
            return True
        else:
            logger.warning(f"外部任务ID与本地任务ID不匹配: local={local_task_id}, external={external_task_id}")
            return False
    
    async def unregister_external_task(self, local_task_id: int) -> bool:
        """
        注销外部任务映射（已简化：无需实际操作）
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            bool: 注销是否成功
        """
        logger.info(f"外部任务映射注销: task_id={local_task_id}")
        return True
    
    async def check_service_health(self) -> bool:
        """
        检查外部服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        return await self.client.check_health()
    
    def get_external_task_id(self, local_task_id: int) -> Optional[int]:
        """
        获取外部任务ID（已简化：直接返回本地任务ID）
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            Optional[int]: 外部任务ID
        """
        return local_task_id
    
    def has_external_mapping(self, local_task_id: int) -> bool:
        """
        检查是否有外部任务映射（已简化：始终返回True）
        
        Args:
            local_task_id: 本地任务ID
            
        Returns:
            bool: 是否有映射
        """
        return True


# 全局通知服务实例
notification_service = NotificationService()
