"""
模型回调服务
处理训练完成后的模型交付回调，支持多种文件传输方式
"""

import os
import asyncio
import logging
import base64
import hashlib
import shutil
import aiofiles
import httpx
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime

from app.models.schemas import ModelDeliveryDTO, ValidationMetricsDTO, FileTransferMethod
from app.storage.memory import task_storage

logger = logging.getLogger(__name__)


class ModelCallbackService:
    """模型回调服务"""
    
    def __init__(self):
        self.callback_endpoints: List[str] = []
        self.enabled = True
        self.default_transfer_method = "base64"  # 默认传输方式
        self.max_file_size = 100 * 1024 * 1024  # 最大文件大小 100MB
        self.supported_methods = ["http_upload", "base64", "copy", "ftp"]
        self.transfer_progress: Dict[int, Dict[str, Any]] = {}  # 传输进度跟踪
        
    async def deliver_trained_model(
        self,
        task_id: int,
        model_path: str,
        performance_metrics: Optional[ValidationMetricsDTO] = None,
        transfer_method: Optional[str] = None,
        upload_endpoint: Optional[str] = None,
        target_path: Optional[str] = None
    ) -> bool:
        """
        交付训练完成的模型，支持多种传输方式
        
        Args:
            task_id: 任务ID
            model_path: 模型文件路径
            performance_metrics: 模型性能指标
            transfer_method: 传输方式 (http_upload, base64, copy, ftp)
            upload_endpoint: 上传端点URL
            target_path: 目标路径（用于copy方式）
            
        Returns:
            bool: 交付是否成功
        """
        if not self.enabled:
            logger.info(f"模型回调服务已禁用，跳过模型交付: task_id={task_id}")
            return True
            
        try:
            task = task_storage.get(task_id)
            if not task:
                logger.error(f"任务不存在: task_id={task_id}")
                return False
                
            # 获取模型文件信息
            model_info = await self._analyze_model_file(model_path)
            if not model_info:
                logger.error(f"无法分析模型文件: {model_path}")
                return False
                
            # 检查文件大小
            if model_info.get("size", 0) > self.max_file_size:
                logger.warning(f"模型文件过大: {model_info.get('size')} bytes, 最大允许: {self.max_file_size} bytes")
                # 对于大文件，默认使用copy方式
                transfer_method = transfer_method or "copy"
            else:
                transfer_method = transfer_method or self.default_transfer_method
                
            # 初始化进度跟踪
            self._init_transfer_progress(task_id, model_path, model_info.get('size', 0), transfer_method)
                
            # 执行文件传输
            file_transfer_info, file_content_base64, file_hash = await self._transfer_model_file(
                model_path, transfer_method, upload_endpoint, target_path, task_id
            )
            
            # 构建模型交付信息
            model_delivery = ModelDeliveryDTO(
                model_path=model_path,
                model_format=model_info.get("format", "pytorch"),
                model_size=model_info.get("size"),
                model_version=f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                training_config=self._extract_training_config(task),
                performance_metrics=performance_metrics,
                file_transfer=file_transfer_info,
                file_content_base64=file_content_base64,
                file_hash=file_hash,
                transfer_status="completed" if file_transfer_info else "failed"
            )
            
            logger.info(f"开始交付模型: task_id={task_id}, model_path={model_path}")
            logger.info(f"模型信息: 大小={model_info.get('size')} bytes, 格式={model_info.get('format')}, 传输方式={transfer_method}")
            
            # 通过external_client发送模型交付信息，但使用任务的notification_host
            try:
                from app.services.external_client import external_client
                
                # 如果任务有自定义的通知host，临时更新客户端配置
                original_host = None
                if task.notification_host:
                    original_host = external_client.base_url
                    external_client.update_host(task.notification_host)
                    logger.info(f"使用任务特定的通知host进行模型交付: {task.notification_host}")
                
                try:
                    # 发送模型交付通知
                    response = await external_client.send_status_change(
                        external_task_id=task_id,
                        status="model_delivered",  # 自定义状态表示模型已交付
                        best_metrics=performance_metrics,
                        output_path=model_path,
                        model_delivery=model_delivery,
                        timestamp=datetime.now()
                    )
                finally:
                    # 恢复原始host配置
                    if original_host:
                        from app.config.settings import external_service_settings
                        external_client.base_url = original_host
                        external_client.status_change_url = f"{original_host}{external_service_settings.status_change_endpoint}"
                        external_client.health_url = f"{original_host}{external_service_settings.health_endpoint}"
                
                if response and response.code == 0:
                    logger.info(f"✅ 模型交付通知发送成功: task_id={task_id}")
                    
                    # 记录交付历史
                    await self._record_delivery_history(task_id, model_delivery)
                    return True
                else:
                    logger.error(f"❌ 模型交付通知发送失败: task_id={task_id}, response={response}")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ 模型交付通知异常: task_id={task_id}, error={str(e)}")
                return False
                
        except Exception as e:
            logger.error(f"模型交付异常: task_id={task_id}, error={str(e)}")
            # 更新进度状态为失败
            if task_id:
                self._update_transfer_progress(task_id, "failed", error=str(e))
            return False
    
    def _init_transfer_progress(self, task_id: int, model_path: str, file_size: int, transfer_method: str):
        """初始化传输进度跟踪"""
        self.transfer_progress[task_id] = {
            "model_path": model_path,
            "file_size": file_size,
            "transfer_method": transfer_method,
            "status": "preparing",
            "progress_percent": 0.0,
            "bytes_transferred": 0,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "error": None
        }
        logger.info(f"初始化传输进度跟踪: task_id={task_id}, 文件大小={file_size} bytes")
    
    def _update_transfer_progress(
        self, 
        task_id: int, 
        status: str, 
        bytes_transferred: Optional[int] = None,
        error: Optional[str] = None
    ):
        """更新传输进度"""
        if task_id not in self.transfer_progress:
            return
            
        progress = self.transfer_progress[task_id]
        progress["status"] = status
        
        if bytes_transferred is not None:
            progress["bytes_transferred"] = bytes_transferred
            if progress["file_size"] > 0:
                progress["progress_percent"] = (bytes_transferred / progress["file_size"]) * 100
        
        if error:
            progress["error"] = error
            
        if status in ["completed", "failed"]:
            progress["end_time"] = datetime.now().isoformat()
            
        logger.debug(f"更新传输进度: task_id={task_id}, 状态={status}, 进度={progress.get('progress_percent', 0):.1f}%")
    
    def get_transfer_progress(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取传输进度"""
        return self.transfer_progress.get(task_id)
    
    def clear_transfer_progress(self, task_id: int):
        """清理传输进度记录"""
        if task_id in self.transfer_progress:
            del self.transfer_progress[task_id]
    
    async def _transfer_model_file(
        self, 
        model_path: str, 
        transfer_method: str,
        upload_endpoint: Optional[str] = None,
        target_path: Optional[str] = None,
        task_id: Optional[int] = None
    ) -> Tuple[Optional[FileTransferMethod], Optional[str], Optional[str]]:
        """
        执行模型文件传输
        
        Args:
            model_path: 模型文件路径
            transfer_method: 传输方式
            upload_endpoint: 上传端点URL
            target_path: 目标路径
            
        Returns:
            tuple: (传输方式信息, Base64编码内容, 文件哈希值)
        """
        try:
            if transfer_method not in self.supported_methods:
                logger.error(f"不支持的传输方式: {transfer_method}")
                return None, None, None
                
            # 计算文件哈希值
            file_hash = await self._calculate_file_hash(model_path)
            
            if transfer_method == "base64":
                return await self._transfer_via_base64(model_path, file_hash, task_id)
            elif transfer_method == "http_upload":
                return await self._transfer_via_http_upload(model_path, upload_endpoint, file_hash, task_id)
            elif transfer_method == "copy":
                return await self._transfer_via_copy(model_path, target_path, file_hash, task_id)
            elif transfer_method == "ftp":
                return await self._transfer_via_ftp(model_path, upload_endpoint, file_hash, task_id)
            else:
                logger.error(f"未实现的传输方式: {transfer_method}")
                return None, None, None
                
        except Exception as e:
            logger.error(f"文件传输失败: {str(e)}")
            return None, None, None
    
    async def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            async with aiofiles.open(file_path, 'rb') as f:
                while chunk := await f.read(8192):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return ""
    
    async def _transfer_via_base64(self, model_path: str, file_hash: str, task_id: Optional[int] = None) -> Tuple[FileTransferMethod, str, str]:
        """通过Base64编码传输文件"""
        try:
            if task_id:
                self._update_transfer_progress(task_id, "encoding")
                
            async with aiofiles.open(model_path, 'rb') as f:
                file_content = await f.read()
                
                if task_id:
                    self._update_transfer_progress(task_id, "encoding", len(file_content))
                    
                base64_content = base64.b64encode(file_content).decode('utf-8')
                
            transfer_info = FileTransferMethod(
                method="base64",
                endpoint=None,
                target_path=None
            )
            
            if task_id:
                self._update_transfer_progress(task_id, "completed", len(file_content))
            
            logger.info(f"✅ Base64编码完成，内容大小: {len(base64_content)} 字符")
            return transfer_info, base64_content, file_hash
            
        except Exception as e:
            logger.error(f"Base64传输失败: {str(e)}")
            raise
    
    async def _transfer_via_http_upload(
        self, model_path: str, upload_endpoint: str, file_hash: str, task_id: Optional[int] = None
    ) -> Tuple[FileTransferMethod, None, str]:
        """通过HTTP上传文件"""
        try:
            if not upload_endpoint:
                raise ValueError("HTTP上传需要指定upload_endpoint")
                
            async with httpx.AsyncClient(timeout=300) as client:
                async with aiofiles.open(model_path, 'rb') as f:
                    files = {'file': (Path(model_path).name, f, 'application/octet-stream')}
                    data = {'file_hash': file_hash}
                    
                    response = await client.post(upload_endpoint, files=files, data=data)
                    
                if response.status_code == 200:
                    transfer_info = FileTransferMethod(
                        method="http_upload",
                        endpoint=upload_endpoint,
                        target_path=None
                    )
                    logger.info(f"✅ HTTP上传成功: {upload_endpoint}")
                    return transfer_info, None, file_hash
                else:
                    raise Exception(f"HTTP上传失败，状态码: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"HTTP上传失败: {str(e)}")
            raise
    
    async def _transfer_via_copy(
        self, model_path: str, target_path: str, file_hash: str, task_id: Optional[int] = None
    ) -> Tuple[FileTransferMethod, None, str]:
        """通过文件复制传输"""
        try:
            if not target_path:
                raise ValueError("文件复制需要指定target_path")
                
            # 确保目标目录存在
            target_dir = Path(target_path).parent
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 执行异步文件复制
            await asyncio.get_event_loop().run_in_executor(
                None, shutil.copy2, model_path, target_path
            )
            
            # 验证复制结果
            if Path(target_path).exists():
                copied_hash = await self._calculate_file_hash(target_path)
                if copied_hash == file_hash:
                    transfer_info = FileTransferMethod(
                        method="copy",
                        endpoint=None,
                        target_path=target_path
                    )
                    logger.info(f"✅ 文件复制成功: {target_path}")
                    return transfer_info, None, file_hash
                else:
                    raise Exception("文件复制后哈希值不匹配")
            else:
                raise Exception("文件复制失败，目标文件不存在")
                
        except Exception as e:
            logger.error(f"文件复制失败: {str(e)}")
            raise
    
    async def _transfer_via_ftp(
        self, model_path: str, ftp_endpoint: str, file_hash: str, task_id: Optional[int] = None
    ) -> Tuple[FileTransferMethod, None, str]:
        """通过FTP传输文件（简化实现）"""
        try:
            # 注意：这里需要安装aioftp库，这是一个简化实现
            logger.warning("FTP传输功能需要额外配置，当前使用模拟实现")
            
            if task_id:
                self._update_transfer_progress(task_id, "uploading")
            
            transfer_info = FileTransferMethod(
                method="ftp",
                endpoint=ftp_endpoint,
                target_path=None
            )
            
            # 这里应该实现真正的FTP上传逻辑
            # 暂时返回成功状态，实际使用时需要实现
            if task_id:
                # 模拟进度更新
                file_size = Path(model_path).stat().st_size
                self._update_transfer_progress(task_id, "completed", file_size)
            
            logger.info(f"📡 FTP传输完成（模拟）: {ftp_endpoint}")
            return transfer_info, None, file_hash
            
        except Exception as e:
            logger.error(f"FTP传输失败: {str(e)}")
            raise
    
    async def _analyze_model_file(self, model_path: str) -> Optional[Dict[str, Any]]:
        """
        分析模型文件信息
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            Dict[str, Any]: 模型文件信息
        """
        try:
            model_file = Path(model_path)
            if not model_file.exists():
                logger.error(f"模型文件不存在: {model_path}")
                return None
                
            # 获取文件信息
            file_stats = model_file.stat()
            file_size = file_stats.st_size
            
            # 根据文件扩展名确定格式
            file_format = "pytorch"
            if model_file.suffix.lower() in ['.pt', '.pth']:
                file_format = "pytorch"
            elif model_file.suffix.lower() in ['.onnx']:
                file_format = "onnx"
            elif model_file.suffix.lower() in ['.tflite']:
                file_format = "tflite"
            elif model_file.suffix.lower() in ['.pb']:
                file_format = "tensorflow"
                
            return {
                "size": file_size,
                "format": file_format,
                "extension": model_file.suffix,
                "name": model_file.name,
                "created_time": datetime.fromtimestamp(file_stats.st_ctime)
            }
            
        except Exception as e:
            logger.error(f"分析模型文件失败: {model_path}, error={str(e)}")
            return None
    
    def _extract_training_config(self, task) -> Dict[str, Any]:
        """
        提取训练配置信息
        
        Args:
            task: 训练任务对象
            
        Returns:
            Dict[str, Any]: 训练配置
        """
        try:
            config = task.config
            return {
                "model_name": config.model_name,
                "epochs": config.epochs,
                "batch_size": config.batch_size,
                "learning_rate": config.learning_rate,
                "imgsz": config.imgsz,
                "device": config.device,
                "optimizer": getattr(config, 'optimizer', 'auto'),
                "data_path": config.data_path,
                "additional_params": config.additional_params
            }
        except Exception as e:
            logger.error(f"提取训练配置失败: {str(e)}")
            return {}
    
    async def _record_delivery_history(self, task_id: int, model_delivery: ModelDeliveryDTO):
        """
        记录模型交付历史
        
        Args:
            task_id: 任务ID
            model_delivery: 模型交付信息
        """
        try:
            from app.storage.metrics import metrics_storage
            
            # 构建交付记录（用于扩展记录功能）
            delivery_record = {
                "timestamp": datetime.now().isoformat(),
                "model_path": model_delivery.model_path,
                "model_size": model_delivery.model_size,
                "model_format": model_delivery.model_format,
                "model_version": model_delivery.model_version,
                "transfer_method": model_delivery.file_transfer.method if model_delivery.file_transfer else None,
                "transfer_status": model_delivery.transfer_status,
                "file_hash": model_delivery.file_hash,
                "performance_metrics": model_delivery.performance_metrics.model_dump() if model_delivery.performance_metrics else None
            }
            
            # 记录到日志系统
            transfer_info = f"传输方式: {delivery_record['transfer_method']}, 状态: {delivery_record['transfer_status']}"
            metrics_storage.add_log(
                task_id, 
                "INFO", 
                f"模型已交付: {model_delivery.model_path} ({model_delivery.model_size} bytes) - {transfer_info}"
            )
            
            logger.info(f"模型交付历史已记录: task_id={task_id}, 记录详情: {delivery_record}")
            
        except Exception as e:
            logger.error(f"记录模型交付历史失败: task_id={task_id}, error={str(e)}")
    
    async def register_callback_endpoint(self, endpoint: str) -> bool:
        """
        注册模型回调端点
        
        Args:
            endpoint: 回调端点URL
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if endpoint not in self.callback_endpoints:
                self.callback_endpoints.append(endpoint)
                logger.info(f"模型回调端点已注册: {endpoint}")
            return True
        except Exception as e:
            logger.error(f"注册模型回调端点失败: {endpoint}, error={str(e)}")
            return False
    
    async def unregister_callback_endpoint(self, endpoint: str) -> bool:
        """
        取消注册模型回调端点
        
        Args:
            endpoint: 回调端点URL
            
        Returns:
            bool: 取消注册是否成功
        """
        try:
            if endpoint in self.callback_endpoints:
                self.callback_endpoints.remove(endpoint)
                logger.info(f"模型回调端点已取消注册: {endpoint}")
            return True
        except Exception as e:
            logger.error(f"取消注册模型回调端点失败: {endpoint}, error={str(e)}")
            return False
    
    def get_callback_endpoints(self) -> List[str]:
        """
        获取所有注册的回调端点
        
        Returns:
            List[str]: 回调端点列表
        """
        return self.callback_endpoints.copy()
    
    def enable(self):
        """启用模型回调服务"""
        self.enabled = True
        logger.info("模型回调服务已启用")
    
    def disable(self):
        """禁用模型回调服务"""
        self.enabled = False
        logger.info("模型回调服务已禁用")
    
    def is_enabled(self) -> bool:
        """检查模型回调服务是否启用"""
        return self.enabled


# 全局模型回调服务实例
model_callback_service = ModelCallbackService()