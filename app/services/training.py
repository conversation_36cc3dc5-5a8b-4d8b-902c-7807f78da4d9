import os
import asyncio
import signal
import psutil
import traceback
import sys
from pathlib import Path
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor
from typing import Dict, Optional

from app.models.enums import TaskStatus
from app.storage.memory import task_storage
from app.storage.metrics import metrics_storage
from app.config.settings import swanlab_settings
from app.services.monitoring import resource_monitor

# 延迟导入通知服务以避免循环导入
notification_service = None

def get_notification_service():
    """获取通知服务实例（延迟导入）"""
    global notification_service
    if notification_service is None:
        from app.services.notification import notification_service as ns
        notification_service = ns
    return notification_service

# 全局进程池和任务管理
process_pool: Optional[ProcessPoolExecutor] = None
running_tasks: Dict[int, asyncio.Task] = {}
training_futures: Dict[int, asyncio.Future] = {}
training_processes: Dict[int, int] = {}  # task_id -> process_id


def log_detailed_error(context: str, exception: Exception, task_id: Optional[int] = None, extra_info: dict = None):
    """
    统一的详细错误日志记录函数

    Args:
        context: 错误发生的上下文描述
        exception: 异常对象
        task_id: 任务ID（可选）
        extra_info: 额外的调试信息（可选）
    """
    error_type = type(exception).__name__
    error_message = str(exception)
    error_traceback = traceback.format_exc()

    print("=" * 80)
    print(f"🚨 {context}" + (f" - Task ID: {task_id}" if task_id else ""))
    print("=" * 80)
    print(f"📍 异常类型: {error_type}")
    print(f"📝 异常消息: {error_message}")
    print(f"⏰ 异常时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔍 进程ID: {os.getpid()}")

    # 打印额外信息
    if extra_info:
        print(f"\n📊 额外信息:")
        for key, value in extra_info.items():
            print(f"   {key}: {value}")

    print("\n📋 完整堆栈跟踪:")
    print("-" * 40)
    print(error_traceback)
    print("-" * 40)
    print("=" * 80)

    return {
        'error_type': error_type,
        'error_message': error_message,
        'error_traceback': error_traceback
    }


def save_error_report(task_id: int, error_info: dict, config_dict: dict = None):
    """
    保存详细的错误报告到文件

    Args:
        task_id: 任务ID
        error_info: 错误信息字典
        config_dict: 配置信息（可选）
    """
    try:
        # 创建错误报告目录
        error_dir = Path("error_reports")
        error_dir.mkdir(exist_ok=True)

        # 生成错误报告文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = error_dir / f"error_task_{task_id}_{timestamp}.txt"

        # 生成错误报告内容
        report_content = []
        report_content.append("=" * 80)
        report_content.append(f"训练任务错误报告 - Task ID: {task_id}")
        report_content.append("=" * 80)
        report_content.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_content.append(f"异常类型: {error_info.get('error_type', 'Unknown')}")
        report_content.append(f"异常消息: {error_info.get('error_message', 'Unknown')}")
        report_content.append("")

        # 系统信息
        report_content.append("系统信息:")
        report_content.append(f"  Python版本: {sys.version}")
        report_content.append(f"  工作目录: {os.getcwd()}")
        report_content.append(f"  进程ID: {os.getpid()}")
        report_content.append("")

        # 配置信息
        if config_dict:
            report_content.append("训练配置:")
            for key, value in config_dict.items():
                if key != 'additional_params':  # 避免过长的参数
                    report_content.append(f"  {key}: {value}")
            report_content.append("")

        # 完整堆栈跟踪
        report_content.append("完整堆栈跟踪:")
        report_content.append("-" * 40)
        report_content.append(error_info.get('error_traceback', 'No traceback available'))
        report_content.append("-" * 40)
        report_content.append("=" * 80)

        # 写入文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        print(f"📄 错误报告已保存到: {report_file}")
        return str(report_file)

    except Exception as e:
        print(f"⚠️ 保存错误报告失败: {e}")
        return None


async def get_process_pool():
    """获取或创建进程池"""
    global process_pool
    if process_pool is None:
        loop = asyncio.get_event_loop()
        process_pool = await loop.run_in_executor(None, lambda: ProcessPoolExecutor(max_workers=2))
    return process_pool


def _run_training_in_process(task_id: int, config_dict: dict):
    """在独立进程中执行训练任务"""
    try:
        # 记录当前进程ID
        current_pid = os.getpid()
        print(f"训练进程启动，PID: {current_pid}, Task ID: {task_id}")

        from ultralytics import YOLO

        # 设置CUDA设备顺序
        os.environ["CUDA_DEVICE_ORDER"] = config_dict.get('cuda_device_order', 'PCI_BUS_ID')

        # 跟踪SwanLab是否已初始化
        swanlab_initialized = False
        swanlab = None

        # 初始化SwanLab
        if config_dict.get('enable_swanlab', False):
            try:
                import swanlab
                from swanlab.integration.ultralytics import add_swanlab_callback

                swanlab.init(
                    project=f"train_task_{task_id}",
                    logdir=config_dict.get('swanlab_logdir', './swanlab_logs'),
                    mode=config_dict.get('swanlab_mode', 'cloud'),
                    experiment_name=f"exp_{task_id}",
                    task_id=task_id
                )
                swanlab_initialized = True
            except Exception as e:
                print(f"⚠️ SwanLab初始化失败: {e}")
                swanlab_initialized = False
                swanlab = None

        # 加载模型
        model = YOLO(config_dict['model_name'])

        # 添加SwanLab回调
        if config_dict.get('enable_swanlab', False):
            add_swanlab_callback(model)

        # 添加自定义进度回调
        def on_train_epoch_end(trainer):
            """训练epoch结束时的回调"""
            try:
                print(f"🔄 Epoch回调被调用 - Task {task_id}, Trainer epoch: {trainer.epoch}")
                current_epoch = trainer.epoch + 1  # YOLO的epoch从0开始
                total_epochs = trainer.epochs
                progress_percent = (current_epoch / total_epochs) * 100

                # 创建进度更新数据
                progress_data = {
                    'task_id': task_id,
                    'epoch': current_epoch,
                    'total_epochs': total_epochs,
                    'progress_percent': progress_percent,
                    'train_loss': float(trainer.loss.item()) if hasattr(trainer, 'loss') and trainer.loss is not None else 0.0,
                    'learning_rate': float(trainer.optimizer.param_groups[0]['lr']) if hasattr(trainer, 'optimizer') else 0.0,
                    'send_external_update': True,  # 标记需要发送外部更新
                    'timestamp': datetime.now().isoformat()  # 添加时间戳用于调试
                }

                # 尝试获取验证指标
                try:
                    if hasattr(trainer, 'metrics') and trainer.metrics:
                        metrics = trainer.metrics
                        # 添加验证指标到进度数据
                        progress_data.update({
                            'precision': float(metrics.get('metrics/precision(B)', 0.0)),
                            'recall': float(metrics.get('metrics/recall(B)', 0.0)),
                            'map50': float(metrics.get('metrics/mAP50(B)', 0.0)),
                            'map50_95': float(metrics.get('metrics/mAP50-95(B)', 0.0)),
                            'val_loss': float(metrics.get('val/box_loss', 0.0))
                        })

                        # 计算F1 score
                        precision = progress_data.get('precision', 0.0)
                        recall = progress_data.get('recall', 0.0)
                        if precision + recall > 0:
                            progress_data['f1_score'] = 2 * (precision * recall) / (precision + recall)
                        else:
                            progress_data['f1_score'] = 0.0

                except Exception as metrics_error:
                    print(f"获取验证指标时出错: {metrics_error}")

                # 保存进度到文件（用于进程间通信）
                progress_file = f"/tmp/training_progress_{task_id}.json"
                import json
                with open(progress_file, 'w') as f:
                    json.dump(progress_data, f)
                print(f"📝 进度文件已保存: {progress_file}")

                # 打印进度信息
                if 'map50_95' in progress_data and progress_data['map50_95'] > 0:
                    print(f"Task {task_id}: Epoch {current_epoch}/{total_epochs} ({progress_percent:.1f}%) - mAP50-95: {progress_data['map50_95']:.4f}")
                else:
                    print(f"Task {task_id}: Epoch {current_epoch}/{total_epochs} ({progress_percent:.1f}%)")
                
                print(f"🚩 外部更新标志已设置: send_external_update={progress_data['send_external_update']}")

            except Exception as e:
                print(f"进度回调错误: {e}")

        # 尝试创建一个更全面的回调管理器
        class TrainingCallbackManager:
            def __init__(self, task_id):
                self.task_id = task_id
                self.last_epoch = -1
                self.callback_count = 0
                print(f"🎛️ 初始化回调管理器 - Task {task_id}")
            
            def on_any_event(self, trainer, event_name="unknown"):
                """通用事件处理器"""
                try:
                    self.callback_count += 1
                    current_epoch = getattr(trainer, 'epoch', -1)
                    
                    if self.callback_count % 50 == 0:  # 每50次回调打印一次状态
                        print(f"📊 回调状态 - Event: {event_name}, Epoch: {current_epoch}, Count: {self.callback_count}")
                    
                    # 只有当epoch真正改变时才触发epoch结束处理
                    if current_epoch != self.last_epoch and current_epoch >= 0:
                        print(f"🆕 Epoch变化检测 - {event_name}: {self.last_epoch} -> {current_epoch}")
                        self.last_epoch = current_epoch
                        on_train_epoch_end(trainer)
                        
                except Exception as e:
                    print(f"❌ 回调处理错误 ({event_name}): {e}")
            
            def epoch_end(self, trainer):
                self.on_any_event(trainer, "epoch_end")
            
            def val_end(self, trainer):
                self.on_any_event(trainer, "val_end")
            
            def batch_end(self, trainer):
                self.on_any_event(trainer, "batch_end")
        
        # 创建回调管理器
        callback_manager = TrainingCallbackManager(task_id)
        
        # 注册多种回调事件
        try:
            model.add_callback('on_train_epoch_end', callback_manager.epoch_end)
            model.add_callback('on_val_end', callback_manager.val_end)
            model.add_callback('on_fit_epoch_end', callback_manager.epoch_end)
            model.add_callback('on_train_batch_end', callback_manager.batch_end)
            print(f"🔗 已注册多种训练回调 - Task {task_id}")
        except Exception as e:
            print(f"⚠️ 回调注册部分失败: {e}")
            # 备用方案：直接注册原始回调
            model.add_callback('on_train_epoch_end', on_train_epoch_end)
        
        # 训练参数
        train_args = {
            "data": config_dict['data_path'],
            "epochs": config_dict['epochs'],
            "imgsz": config_dict['imgsz'],
            "batch": config_dict['batch_size'],
            "lr0": config_dict['learning_rate'],
            "device": config_dict['device'],
            "project": config_dict['project'],
            "name": config_dict.get('name', f"exp_{str(task_id)[:8]}"),
            "save_period": config_dict.get('save_period', -1),
            "patience": config_dict.get('patience', 50),
            "cache": config_dict.get('cache', False),
            **config_dict.get('additional_params', {})
        }
        
        print(f"🎯 训练参数已配置 - Task {task_id}")
        print(f"   Epochs: {config_dict['epochs']}")
        print(f"   Data: {config_dict['data_path']}")
        print(f"   Device: {config_dict['device']}")
        print(f"   Project: {config_dict['project']}")
        
        print(f"🚀 开始YOLO训练 - Task {task_id}")
        
        # 开始训练
        results = model.train(**train_args)
        
        print(f"🏁 YOLO训练完成 - Task {task_id}")
        print(f"   总回调次数: {callback_manager.callback_count if 'callback_manager' in locals() else 'N/A'}")
        print(f"   最后epoch: {callback_manager.last_epoch if 'callback_manager' in locals() else 'N/A'}")
        
        # # 结束SwanLab记录
        # if swanlab_initialized and swanlab is not None:
        #     try:
        #         swanlab.finish()
        #     except Exception as e:
        #         print(f"⚠️ SwanLab结束时出错: {e}")
        
        # 提取所有验证指标
        best_metrics = {}
        if hasattr(results, 'results_dict') and results.results_dict:
            results_dict = results.results_dict
            best_metrics = {
                'precision': results_dict.get('metrics/precision(B)', 0.0),
                'recall': results_dict.get('metrics/recall(B)', 0.0),
                'map50': results_dict.get('metrics/mAP50(B)', 0.0),
                'map50_95': results_dict.get('metrics/mAP50-95(B)', 0.0),
                'f1_score': 0.0  # F1 score需要从precision和recall计算
            }

            # 计算F1 score
            precision = best_metrics['precision']
            recall = best_metrics['recall']
            if precision + recall > 0:
                best_metrics['f1_score'] = 2 * (precision * recall) / (precision + recall)
        else:
            # 如果results_dict不可用，尝试从CSV文件读取最后一行的指标
            try:
                import pandas as pd

                # 构建results.csv文件路径
                project_path = config_dict.get('project', 'runs/train')
                name = config_dict.get('name', 'exp')
                results_csv_path = os.path.join(project_path, name, 'results.csv')

                if os.path.exists(results_csv_path):
                    df = pd.read_csv(results_csv_path)
                    if not df.empty:
                        last_row = df.iloc[-1]  # 获取最后一行（最佳结果）

                        precision = float(last_row.get('metrics/precision(B)', 0.0))
                        recall = float(last_row.get('metrics/recall(B)', 0.0))
                        map50 = float(last_row.get('metrics/mAP50(B)', 0.0))
                        map50_95 = float(last_row.get('metrics/mAP50-95(B)', 0.0))

                        # 计算F1 score
                        f1_score = 0.0
                        if precision + recall > 0:
                            f1_score = 2 * (precision * recall) / (precision + recall)

                        best_metrics = {
                            'precision': precision,
                            'recall': recall,
                            'map50': map50,
                            'map50_95': map50_95,
                            'f1_score': f1_score
                        }

                        print(f"📊 从CSV文件读取到的最佳指标: {best_metrics}")
                    else:
                        print("⚠️ results.csv文件为空")
                else:
                    print(f"⚠️ results.csv文件不存在: {results_csv_path}")

            except Exception as csv_error:
                print(f"⚠️ 从CSV文件读取指标失败: {csv_error}")
                best_metrics = {
                    'precision': 0.0,
                    'recall': 0.0,
                    'map50': 0.0,
                    'map50_95': 0.0,
                    'f1_score': 0.0
                }

        return {
            'success': True,
            'best_metrics': best_metrics,
            'message': '训练完成'
        }
        
    except Exception as e:
        # 使用统一的错误日志记录函数
        error_info = log_detailed_error("训练进程异常", e, task_id)

        # 打印配置信息用于调试
        print("\n⚙️ 训练配置信息:")
        for key, value in config_dict.items():
            if key not in ['additional_params']:  # 避免打印过长的参数
                print(f"   {key}: {value}")

        # 检查CUDA相关错误
        if 'cuda' in error_info['error_message'].lower() or 'gpu' in error_info['error_message'].lower():
            print("\n🔥 检测到CUDA/GPU相关错误:")
            try:
                import torch
                print(f"   PyTorch版本: {torch.__version__}")
                print(f"   CUDA可用: {torch.cuda.is_available()}")
                if torch.cuda.is_available():
                    print(f"   CUDA版本: {torch.version.cuda}")
                    print(f"   GPU数量: {torch.cuda.device_count()}")
                    for i in range(torch.cuda.device_count()):
                        print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            except ImportError:
                print("   无法导入PyTorch检查CUDA状态")

        # 检查文件路径相关错误
        if 'no such file' in error_info['error_message'].lower() or 'not found' in error_info['error_message'].lower():
            print("\n📁 检测到文件路径相关错误:")
            data_path = config_dict.get('data_path', '')
            model_name = config_dict.get('model_name', '')
            project_path = config_dict.get('project', '')

            if data_path:
                print(f"   数据路径存在: {os.path.exists(data_path)} - {data_path}")
            if model_name:
                print(f"   模型文件: {model_name}")
            if project_path:
                print(f"   项目路径存在: {os.path.exists(project_path)} - {project_path}")

        # 保存详细的错误报告到文件
        save_error_report(task_id, error_info, config_dict)

        # 清理SwanLab
        try:
            if swanlab_initialized and swanlab is not None:
                swanlab.finish()
        except Exception as swanlab_error:
            print(f"⚠️ 清理SwanLab时出错: {swanlab_error}")

        return {
            'success': False,
            'error': error_info['error_message'],
            'error_type': error_info['error_type'],
            'error_traceback': error_info['error_traceback'],
            'message': f'训练失败: {error_info["error_type"]}: {error_info["error_message"]}'
        }


async def _update_training_progress(task_id: int):
    """定期更新训练进度"""
    import json
    progress_file = f"/tmp/training_progress_{task_id}.json"
    notification_counter = 0  # 用于控制通知频率
    
    # 导入所需模块
    from app.models.schemas import TrainingProgressDTO, ValidationMetricsDTO, ResourceUsageDTO

    while True:
        try:
            # 检查进度文件是否存在
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress_data = json.load(f)
                
                print(f"📖 读取进度文件: epoch={progress_data.get('epoch', 0)}, "
                      f"external_flag={progress_data.get('send_external_update', False)}, "
                      f"timestamp={progress_data.get('timestamp', 'N/A')}")

                # 更新任务进度
                task = task_storage.get(task_id)
                if task:
                    task.progress.epoch = progress_data.get('epoch', 0)
                    task.progress.total_epochs = progress_data.get('total_epochs', 0)
                    task.progress.progress_percent = progress_data.get('progress_percent', 0.0)
                    task.progress.train_loss = progress_data.get('train_loss', 0.0)
                    task.progress.learning_rate = progress_data.get('learning_rate', 0.0)
                    task.progress.val_loss = progress_data.get('val_loss', 0.0)

                    # 更新当前验证指标（如果有的话）
                    if 'precision' in progress_data:
                        task.current_metrics.precision = progress_data.get('precision', 0.0)
                        task.current_metrics.recall = progress_data.get('recall', 0.0)
                        task.current_metrics.map50 = progress_data.get('map50', 0.0)
                        task.current_metrics.map50_95 = progress_data.get('map50_95', 0.0)
                        task.current_metrics.f1_score = progress_data.get('f1_score', 0.0)

                    # 保存指标到历史记录
                    metrics_to_save = {
                        'train_loss': progress_data.get('train_loss', 0.0),
                        'learning_rate': progress_data.get('learning_rate', 0.0),
                        'progress_percent': progress_data.get('progress_percent', 0.0)
                    }

                    # 添加验证指标到历史记录（如果有的话）
                    if 'precision' in progress_data:
                        metrics_to_save.update({
                            'precision': progress_data.get('precision', 0.0),
                            'recall': progress_data.get('recall', 0.0),
                            'map50': progress_data.get('map50', 0.0),
                            'map50_95': progress_data.get('map50_95', 0.0),
                            'f1_score': progress_data.get('f1_score', 0.0),
                            'val_loss': progress_data.get('val_loss', 0.0)
                        })

                    metrics_storage.save_metrics(task_id, progress_data.get('epoch', 0), metrics_to_save)

                    # 检查是否需要发送外部服务更新（每个epoch一次）
                    if progress_data.get('send_external_update', False):
                        print(f"🔥 检测到外部更新标志，准备发送epoch {progress_data.get('epoch', 0)}的进度")
                        try:
                            # 直接使用本地任务ID作为外部任务ID
                            external_task_id = task_id
                            
                            # 构建进度信息（匹配外部服务期望格式）
                            current_epoch = progress_data.get('epoch', 0)
                            
                            # 训练进度
                            progress_info = TrainingProgressDTO(
                                epoch=current_epoch,  # 注意：字段名改为epoch而不是current_epoch
                                total_epochs=progress_data.get('total_epochs', 0),
                                progress_percent=progress_data.get('progress_percent', 0.0),
                                train_loss=progress_data.get('train_loss', 0.0),
                                val_loss=progress_data.get('val_loss', 0.0),
                                learning_rate=progress_data.get('learning_rate', 0.0)
                            )
                            
                            # 当前指标
                            current_metrics = ValidationMetricsDTO(
                                precision=progress_data.get('precision'),
                                recall=progress_data.get('recall'),
                                map50=progress_data.get('map50'),
                                map50_95=progress_data.get('map50_95'),
                                f1_score=progress_data.get('f1_score')
                            )
                            
                            # 资源使用（从任务对象获取）
                            resource_usage = None
                            if task and task.resource_usage:
                                resource_usage = ResourceUsageDTO(
                                    gpu_memory_used=task.resource_usage.gpu_memory_used,
                                    gpu_memory_total=task.resource_usage.gpu_memory_total,
                                    gpu_utilization=task.resource_usage.gpu_utilization,
                                    cpu_percent=task.resource_usage.cpu_percent,
                                    memory_percent=task.resource_usage.memory_percent
                                )
                            
                            print(f"📤 准备发送外部更新: external_task_id={external_task_id}, current_epoch={current_epoch}")
                            
                            # 通过notification_service发送状态更新（确保使用正确的host）
                            try:
                                ns = get_notification_service()
                                success = await ns.notify_progress_update(task)
                                if success:
                                    print(f"✅ 已发送epoch {current_epoch}训练进度到外部服务")
                                else:
                                    print(f"⚠️ 发送epoch {current_epoch}训练进度到外部服务失败")
                            except Exception as notify_error:
                                print(f"⚠️ 发送训练进度通知异常: {notify_error}")
                                
                            # 清除标志，避免重复发送
                            progress_data['send_external_update'] = False
                            with open(progress_file, 'w') as f:
                                json.dump(progress_data, f)
                            print(f"🏁 外部更新标志已清除")
                                
                        except Exception as external_error:
                            print(f"⚠️ 发送外部服务更新失败: {external_error}")
                    else:
                        print(f"⏭️ 无外部更新标志，跳过外部服务通知")

                    # 每隔一定时间发送进度通知（避免过于频繁）
                    notification_counter += 1
                    if notification_counter % 6 == 0:  # 每30秒发送一次进度通知
                        try:
                            ns = get_notification_service()
                            await ns.notify_progress_update(task)
                        except Exception as e:
                            print(f"⚠️ 发送进度更新通知失败: {e}")

            await asyncio.sleep(5)  # 每5秒更新一次

        except Exception as e:
            print(f"更新进度时出错: {e}")
            await asyncio.sleep(5)


async def _monitor_training_task(task_id: int, future):
    """监控训练任务的后台协程"""
    try:
        # 创建一个任务来定期更新进度
        progress_task = asyncio.create_task(_update_training_progress(task_id))

        # 等待训练任务完成
        result = await asyncio.wrap_future(future)

        # 取消进度更新任务
        progress_task.cancel()
        try:
            await progress_task
        except asyncio.CancelledError:
            pass

        # 处理训练结果
        task = task_storage.get(task_id)
        if not task:
            return
            
        # 导入模型回调服务
        from app.services.model_callback import model_callback_service
        from app.models.schemas import ValidationMetricsDTO
            
        if result['success']:
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            # 正确更新进度信息
            task.progress.epoch = task.config.epochs
            task.progress.total_epochs = task.config.epochs
            task.progress.progress_percent = 100.0

            # 更新所有最佳指标
            best_metrics = result.get('best_metrics', {})
            task.best_metrics.precision = best_metrics.get('precision', 0.0)
            task.best_metrics.recall = best_metrics.get('recall', 0.0)
            task.best_metrics.map50 = best_metrics.get('map50', 0.0)
            task.best_metrics.map50_95 = best_metrics.get('map50_95', 0.0)
            task.best_metrics.f1_score = best_metrics.get('f1_score', 0.0)

            metrics_storage.add_log(task_id, "INFO", f"训练任务完成 - 最佳指标: mAP50-95={task.best_metrics.map50_95:.4f}, Precision={task.best_metrics.precision:.4f}, Recall={task.best_metrics.recall:.4f}, F1={task.best_metrics.f1_score:.4f}")

            # 模型交付回调 - 查找最佳模型文件
            try:
                best_model_path = await _find_best_model_path(task)
                if best_model_path:
                    # 构建性能指标
                    performance_metrics = ValidationMetricsDTO(
                        precision=task.best_metrics.precision,
                        recall=task.best_metrics.recall,
                        map50=task.best_metrics.map50,
                        map50_95=task.best_metrics.map50_95,
                        f1_score=task.best_metrics.f1_score
                    )
                    
                    # 执行模型交付回调
                    delivery_success = await model_callback_service.deliver_trained_model(
                        task_id=task_id,
                        model_path=best_model_path,
                        performance_metrics=performance_metrics,
                        upload_endpoint='base64',
                    )
                    
                    if delivery_success:
                        metrics_storage.add_log(task_id, "INFO", f"模型交付回调成功: {best_model_path}")
                    else:
                        metrics_storage.add_log(task_id, "WARNING", f"模型交付回调失败: {best_model_path}")
                else:
                    metrics_storage.add_log(task_id, "WARNING", "未找到训练完成的模型文件")
                    
            except Exception as callback_error:
                metrics_storage.add_log(task_id, "ERROR", f"模型交付回调异常: {str(callback_error)}")
                print(f"⚠️ 模型交付回调异常: {callback_error}")


            # 发送完成状态通知
            try:
                ns = get_notification_service()
                await ns.notify_status_change(task)
            except Exception as e:
                print(f"⚠️ 发送完成状态通知失败: {e}")

        else:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = result.get('error', '未知错误')
            metrics_storage.add_log(task_id, "ERROR", f"训练失败: {result.get('error', '未知错误')}")

            # 任务状态和错误信息已经更新，无需额外处理

            # 发送失败状态通知
            try:
                ns = get_notification_service()
                await ns.notify_status_change(task)
            except Exception as e:
                print(f"⚠️ 发送失败状态通知失败: {e}")

        # 停止资源监控
        resource_monitor.stop_monitoring(task_id)
        
    except Exception as e:
        # 使用统一的错误日志记录函数
        error_info = log_detailed_error("监控任务异常", e, task_id)

        task = task_storage.get(task_id)
        if task:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = f"{error_info['error_type']}: {error_info['error_message']}"

            # 发送异常失败状态通知
            try:
                ns = get_notification_service()
                await ns.notify_status_change(task)
            except Exception as notify_e:
                print(f"⚠️ 发送异常失败状态通知失败: {notify_e}")

        resource_monitor.stop_monitoring(task_id)
        metrics_storage.add_log(task_id, "ERROR", f"监控失败: {error_info['error_type']}: {error_info['error_message']}")

        # 保存错误报告
        save_error_report(task_id, error_info)
        
    finally:
        # 清理进度文件
        progress_file = f"/tmp/training_progress_{task_id}.json"
        try:
            if os.path.exists(progress_file):
                os.remove(progress_file)
        except Exception as e:
            print(f"清理进度文件失败: {e}")

        # 清理任务记录
        if task_id in running_tasks:
            del running_tasks[task_id]
        if task_id in training_futures:
            del training_futures[task_id]


async def start_training(task_id: int):
    """启动训练任务（非阻塞）"""
    try:
        task = task_storage.get(task_id)
        if not task:
            raise ValueError(f"任务 {task_id} 不存在")
            
        # 检查是否已经在运行
        if task_id in running_tasks:
            raise ValueError(f"任务 {task_id} 已经在运行中")
            
        previous_status = task.status
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()

        # 开始监控资源使用
        resource_monitor.start_monitoring(task_id)
        metrics_storage.add_log(task_id, "INFO", "开始训练任务")
        # 发送状态变更通知
        try:
            ns = get_notification_service()
            await ns.notify_status_change(task, previous_status)
        except Exception as e:
            print(f"⚠️ 发送状态变更通知失败: {e}")
            # 不影响训练任务继续执行

        config = task.config
        
        # 处理数据集下载（如果提供了下载地址和hash值）
        if config.dataset_download_url and config.dataset_hash:
            print(f"🔄 检测到数据集下载配置，开始处理数据集...")
            print(f"   下载地址: {config.dataset_download_url}")
            print(f"   Hash值: {config.dataset_hash}")
            
            # 更新任务状态为上传中（下载数据集）
            previous_status = task.status
            task.status = TaskStatus.UPLOADING
            metrics_storage.add_log(task_id, "INFO", "开始下载数据集")
            
            # 发送状态变更通知
            try:
                ns = get_notification_service()
                await ns.notify_status_change(task, previous_status)
            except Exception as e:
                print(f"⚠️ 发送下载状态通知失败: {e}")
            
            try:
                # 导入数据集下载器
                from app.utils.dataset_downloader import dataset_downloader
                
                # 下载并解压数据集
                dataset_path = await dataset_downloader.download_and_extract_dataset(
                    download_url=config.dataset_download_url,
                    dataset_hash=config.dataset_hash
                )
                
                if not dataset_path:
                    raise ValueError("数据集下载或解压失败")
                
                print(f"✅ 数据集处理完成: {dataset_path}")
                metrics_storage.add_log(task_id, "INFO", f"数据集下载解压完成: {dataset_path}")
                
                # 更新数据路径为解压后的目录中的data.yaml文件
                original_data_path = config.data_path

                # 首先查找data.yaml文件
                data_yaml_path = os.path.join(dataset_path, "data.yaml")
                if os.path.exists(data_yaml_path):
                    # 更新data.yaml文件中的path字段
                    try:
                        import yaml

                        # 读取原始data.yaml内容
                        with open(data_yaml_path, 'r', encoding='utf-8') as f:
                            data_config = yaml.safe_load(f)

                        # 更新path字段为数据集的绝对路径
                        data_config['path'] = os.path.abspath(dataset_path)

                        # 写回data.yaml文件
                        with open(data_yaml_path, 'w', encoding='utf-8') as f:
                            yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)

                        print(f"📝 已更新data.yaml中的path字段: {data_config['path']}")
                        metrics_storage.add_log(task_id, "INFO", f"已更新data.yaml中的path字段: {data_config['path']}")

                    except Exception as yaml_error:
                        print(f"⚠️ 更新data.yaml文件失败: {yaml_error}")
                        metrics_storage.add_log(task_id, "WARNING", f"更新data.yaml文件失败: {yaml_error}")

                    config.data_path = data_yaml_path
                    print(f"🔄 更新数据路径为data.yaml: {original_data_path} -> {data_yaml_path}")
                    metrics_storage.add_log(task_id, "INFO", f"数据路径已更新为data.yaml: {data_yaml_path}")
                elif not os.path.isabs(original_data_path):
                    # 如果没有data.yaml，且原路径是相对路径，则在解压目录中查找
                    new_data_path = os.path.join(dataset_path, original_data_path)
                    if os.path.exists(new_data_path):
                        config.data_path = new_data_path
                        print(f"🔄 更新数据路径: {original_data_path} -> {new_data_path}")
                        metrics_storage.add_log(task_id, "INFO", f"数据路径已更新: {new_data_path}")
                    else:
                        print(f"⚠️ 在解压目录中未找到指定的数据文件: {new_data_path}")
                        # 尝试直接使用解压目录
                        config.data_path = dataset_path
                        print(f"🔄 使用解压目录作为数据路径: {dataset_path}")
                        metrics_storage.add_log(task_id, "INFO", f"使用解压目录作为数据路径: {dataset_path}")
                else:
                    # 绝对路径，检查是否存在
                    if os.path.exists(original_data_path):
                        print(f"✅ 使用原始数据路径: {original_data_path}")
                    else:
                        print(f"⚠️ 原始数据路径不存在，使用解压目录: {original_data_path} -> {dataset_path}")
                        config.data_path = dataset_path
                        metrics_storage.add_log(task_id, "INFO", f"数据路径已更新为解压目录: {dataset_path}")
                
            except Exception as dataset_error:
                error_msg = f"数据集下载处理失败: {str(dataset_error)}"
                print(f"❌ {error_msg}")
                metrics_storage.add_log(task_id, "ERROR", error_msg)
                
                # 更新任务状态为失败
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error_message = error_msg
                
                # 发送失败通知
                try:
                    ns = get_notification_service()
                    await ns.notify_status_change(task)
                except Exception as e:
                    print(f"⚠️ 发送失败状态通知失败: {e}")
                
                # 停止资源监控
                resource_monitor.stop_monitoring(task_id)
                return
            
            # 数据集处理完成，恢复为运行状态
            task.status = TaskStatus.RUNNING
            print(f"🎯 数据集处理完成，继续训练任务...")
        else:
            print(f"ℹ️ 未提供数据集下载配置，使用原始数据路径: {config.data_path}")

        # 初始化训练进度
        task.progress.epoch = 0
        task.progress.total_epochs = config.epochs
        task.progress.progress_percent = 0.0
        
        # 创建输出目录（异步方式）
        output_dir = Path(config.project) / (config.name or f"exp_{str(task_id)}")
        # loop = asyncio.get_event_loop()
        # await loop.run_in_executor(None, lambda: output_dir.mkdir(parents=True, exist_ok=True))
        task.output_path = str(output_dir)
        
        # 处理设备配置 - 将逗号分割的字符串转换为适合训练的格式
        device_config = config.device
        print(f"🔧 原始设备配置: {device_config} (类型: {type(device_config)})")

        if isinstance(device_config, str) and device_config not in ['auto', 'cpu']:
            # 如果是逗号分割的字符串，需要处理
            if ',' in device_config:
                print(f"🔧 检测到逗号分割的设备配置，开始处理...")
                # 分割字符串并清理每个设备ID
                device_parts = [part.strip() for part in device_config.split(',')]
                print(f"🔧 分割后的设备部分: {device_parts}")

                # 过滤掉非数字的部分（如任务ID前缀）
                clean_devices = []
                for part in device_parts:
                    original_part = part
                    # 如果包含连字符，取连字符后的部分
                    if '-' in part:
                        part = part.split('-')[-1]
                    # 检查是否为数字
                    if part.isdigit():
                        clean_devices.append(part)
                        print(f"🔧 有效设备ID: {original_part} -> {part}")
                    else:
                        print(f"🔧 跳过无效设备ID: {original_part}")

                # 如果有有效的设备ID，重新组合
                if clean_devices:
                    device_config = ','.join(clean_devices)
                    print(f"🔧 清理后的设备配置: {device_config}")
                else:
                    device_config = 'auto'  # 如果没有有效设备，使用auto
                    print(f"🔧 没有有效设备ID，使用auto")
            # 如果是单个数字字符串，直接使用
            elif device_config.isdigit():
                print(f"🔧 单个数字设备ID，直接使用: {device_config}")
            else:
                device_config = 'auto'  # 其他情况使用auto
                print(f"🔧 无法识别的设备配置，使用auto")
        else:
            print(f"🔧 使用原始设备配置: {device_config}")

        print(f"🔧 最终设备配置: {device_config}")

        # 准备配置字典传递给子进程
        config_dict = {
            'model_name': config.model_name,
            'data_path': config.data_path,
            'epochs': config.epochs,
            'imgsz': config.imgsz,
            'batch_size': config.batch_size,
            'learning_rate': config.learning_rate,
            'device': device_config,
            'project': config.project,
            'name': config.name,
            'save_period': config.save_period,
            'patience': config.patience,
            'cache': config.cache,
            'additional_params': config.additional_params,
            'cuda_device_order': swanlab_settings.cuda_device_order,
            'enable_swanlab': swanlab_settings.enable_swanlab,
            'swanlab_logdir': swanlab_settings.logdir,
            'swanlab_mode': swanlab_settings.mode
        }
        
        # 获取进程池并提交训练任务
        pool = await get_process_pool()
        
        # 在子进程中执行训练（不等待结果）
        future = pool.submit(_run_training_in_process, task_id, config_dict)
        training_futures[task_id] = future
        
        # 创建监控任务在后台运行
        monitor_task = asyncio.create_task(_monitor_training_task(task_id, future))
        running_tasks[task_id] = monitor_task
        
        # 立即返回，不等待训练完成
        metrics_storage.add_log(task_id, "INFO", "训练任务已提交到后台")
        
    except Exception as e:
        # 使用统一的错误日志记录函数
        error_info = log_detailed_error("启动训练任务异常", e, task_id)

        # 打印任务配置信息用于调试
        task = task_storage.get(task_id)
        if task:
            print("\n⚙️ 任务配置信息:")
            config = task.config
            print(f"   模型: {config.model_name}")
            print(f"   数据路径: {config.data_path}")
            print(f"   训练轮数: {config.epochs}")
            print(f"   批次大小: {config.batch_size}")
            print(f"   设备: {config.device}")
            print(f"   项目路径: {config.project}")

        task = task_storage.get(task_id)
        if task:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = f"{error_info['error_type']}: {error_info['error_message']}"

        resource_monitor.stop_monitoring(task_id)
        metrics_storage.add_log(task_id, "ERROR", f"启动训练失败: {error_info['error_type']}: {error_info['error_message']}")

        # 保存错误报告
        config_dict = {}
        if task:
            config = task.config
            config_dict = {
                'model_name': config.model_name,
                'data_path': config.data_path,
                'epochs': config.epochs,
                'batch_size': config.batch_size,
                'device': config.device,
                'project': config.project
            }
        save_error_report(task_id, error_info, config_dict)

        # 清理记录
        if task_id in running_tasks:
            del running_tasks[task_id]
        if task_id in training_futures:
            del training_futures[task_id]


async def stop_training(task_id: int):
    """停止训练任务"""
    task = task_storage.get(task_id)
    if not task:
        raise ValueError(f"任务 {task_id} 不存在")

    if task.status not in [TaskStatus.RUNNING, TaskStatus.PENDING]:
        raise ValueError(f"任务 {task_id} 当前状态为 {task.status.value}，无法停止")

    print(f"正在停止训练任务: {task_id}")

    # 取消监控任务
    if task_id in running_tasks:
        running_tasks[task_id].cancel()
        del running_tasks[task_id]
        print(f"已取消监控任务: {task_id}")

    # 强制停止进程池中的任务
    if task_id in training_futures:
        future = training_futures[task_id]

        # 尝试优雅取消
        cancelled = future.cancel()
        if not cancelled:
            print(f"任务 {task_id} 正在运行，尝试强制终止...")

            # 如果无法取消，说明任务正在运行，需要强制终止
            try:
                # 方法1: 强制关闭进程池
                global process_pool
                if process_pool:
                    print(f"强制关闭进程池以停止任务 {task_id}")
                    await _force_shutdown_process_pool()
                    process_pool = None
                    print("进程池已重新创建")

                # 方法2: 查找并终止相关训练进程
                print("查找并终止训练进程...")
                await force_kill_training_processes()

            except Exception as e:
                print(f"强制停止进程时出错: {e}")
        else:
            print(f"成功取消任务: {task_id}")

        del training_futures[task_id]

    # 清理进程ID记录
    if task_id in training_processes:
        del training_processes[task_id]

    # 清理所有相关的全局状态
    await _cleanup_task_resources(task_id)

    # 清理进度文件
    progress_file = f"/tmp/training_progress_{task_id}.json"
    try:
        if os.path.exists(progress_file):
            os.remove(progress_file)
    except Exception as e:
        print(f"清理进度文件失败: {e}")

    # 更新任务状态
    previous_status = task.status
    task.status = TaskStatus.STOPPED
    task.completed_at = datetime.now()

    # 停止资源监控
    resource_monitor.stop_monitoring(task_id)
    metrics_storage.add_log(task_id, "WARNING", "任务已被停止")

    # 任务状态已更新，将通过notification_service统一发送通知

    # 发送停止状态通知
    try:
        ns = get_notification_service()
        await ns.notify_status_change(task, previous_status)
    except Exception as e:
        print(f"⚠️ 发送停止状态通知失败: {e}")

    print(f"任务 {task_id} 停止完成")
    return True


async def _cleanup_task_resources(task_id: int):
    """清理任务相关的所有资源"""
    print(f"🧹 清理任务 {task_id} 的资源...")

    # 清理运行任务记录
    if task_id in running_tasks:
        try:
            running_tasks[task_id].cancel()
            del running_tasks[task_id]
            print(f"   ✅ 已清理运行任务记录")
        except Exception as e:
            print(f"   ❌ 清理运行任务记录失败: {e}")

    # 清理训练future记录
    if task_id in training_futures:
        try:
            future = training_futures[task_id]
            future.cancel()
            del training_futures[task_id]
            print(f"   ✅ 已清理训练future记录")
        except Exception as e:
            print(f"   ❌ 清理训练future记录失败: {e}")

    # 清理进程ID记录
    if task_id in training_processes:
        try:
            del training_processes[task_id]
            print(f"   ✅ 已清理进程ID记录")
        except Exception as e:
            print(f"   ❌ 清理进程ID记录失败: {e}")

    print(f"   🎯 任务 {task_id} 资源清理完成")


async def _force_shutdown_process_pool():
    """强制关闭进程池"""
    global process_pool
    if process_pool:
        print("🔥 强制关闭进程池...")
        try:
            # 获取进程池中的所有工作进程
            if hasattr(process_pool, '_processes'):
                worker_pids = list(process_pool._processes.keys())
                print(f"   发现 {len(worker_pids)} 个工作进程: {worker_pids}")

                # 直接终止所有工作进程
                for pid in worker_pids:
                    try:
                        worker_proc = psutil.Process(pid)
                        print(f"   终止工作进程 PID: {pid}")
                        worker_proc.terminate()
                        try:
                            worker_proc.wait(timeout=3)
                            print(f"   ✅ 工作进程 {pid} 已终止")
                        except psutil.TimeoutExpired:
                            worker_proc.kill()
                            print(f"   💀 工作进程 {pid} 已强制终止")
                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        print(f"   ❌ 无法终止工作进程 {pid}: {e}")

            # 关闭进程池
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, lambda: process_pool.shutdown(wait=False))
            print("   ✅ 进程池已关闭")

        except Exception as e:
            print(f"   ❌ 关闭进程池时出错: {e}")
        finally:
            process_pool = None


async def force_kill_training_processes():
    """强制终止所有训练相关的进程"""
    try:
        killed_count = 0
        training_keywords = [
            'yolo', 'ultralytics', 'train', 'model.train',
            '_run_training_in_process', 'ProcessPoolExecutor',
            'yolov8', 'yolov5', 'detectron', 'mmdetection'
        ]

        print("🔍 搜索所有可能的训练进程...")

        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'ppid']):
            try:
                proc_info = proc.info
                if not proc_info['name']:
                    continue

                # 检查进程名称和命令行
                name_lower = proc_info['name'].lower()
                cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
                cmdline_lower = cmdline.lower()

                # 更广泛的匹配条件
                should_kill = False

                # 1. Python进程且包含训练关键词
                if 'python' in name_lower:
                    if any(keyword in cmdline_lower for keyword in training_keywords):
                        should_kill = True
                        print(f"🎯 发现Python训练进程 PID: {proc_info['pid']}")
                        print(f"   命令: {cmdline[:150]}...")

                # 2. 直接匹配进程名
                elif any(keyword in name_lower for keyword in ['yolo', 'train']):
                    should_kill = True
                    print(f"🎯 发现训练进程 PID: {proc_info['pid']}, 名称: {proc_info['name']}")

                # 3. 检查是否是我们进程池的子进程
                elif proc_info['ppid'] and 'python' in name_lower:
                    try:
                        parent = psutil.Process(proc_info['ppid'])
                        parent_cmdline = ' '.join(parent.cmdline()).lower()
                        if 'train_manger' in parent_cmdline or 'uvicorn' in parent_cmdline:
                            should_kill = True
                            print(f"🎯 发现子进程 PID: {proc_info['pid']}, 父进程: {proc_info['ppid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                if should_kill:
                    print(f"🔪 正在终止进程 PID: {proc_info['pid']}")
                    try:
                        # 先发送SIGTERM信号
                        proc.terminate()
                        print(f"   发送SIGTERM信号到 PID: {proc_info['pid']}")

                        # 等待进程结束
                        try:
                            proc.wait(timeout=5)
                            print(f"✅ 进程 {proc_info['pid']} 已优雅终止")
                        except psutil.TimeoutExpired:
                            # 如果5秒后还没结束，强制终止
                            print(f"   进程 {proc_info['pid']} 未响应SIGTERM，发送SIGKILL")
                            proc.kill()
                            try:
                                proc.wait(timeout=2)
                                print(f"💀 进程 {proc_info['pid']} 已强制终止")
                            except psutil.TimeoutExpired:
                                print(f"⚠️  进程 {proc_info['pid']} 可能仍在运行")

                        killed_count += 1

                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        print(f"❌ 无法终止进程 {proc_info['pid']}: {e}")

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        if killed_count > 0:
            print(f"📊 总共终止了 {killed_count} 个训练进程")
        else:
            print("❌ 未发现需要终止的训练进程")

        # 额外检查：查找所有Python进程并显示
        print("\n🔍 当前所有Python进程:")
        python_count = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    print(f"   PID: {proc.info['pid']}, 命令: {cmdline[:100]}...")
                    python_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        print(f"   总共 {python_count} 个Python进程")

    except Exception as e:
        error_info = log_detailed_error("强制终止进程异常", e)
        print(f"❌ 强制终止进程时出错: {error_info['error_type']}: {error_info['error_message']}")





async def _find_best_model_path(task) -> Optional[str]:
    """
    查找训练完成后的最佳模型文件路径
    
    Args:
        task: 训练任务对象
        
    Returns:
        Optional[str]: 最佳模型文件路径，未找到时返回None
    """
    try:
        # 构建可能的模型路径
        output_dir = Path(task.output_path) if task.output_path else Path(task.config.project) / (task.config.name or f"exp_{task.task_id}")
        
        # YOLO训练通常会生成以下模型文件
        possible_model_files = [
            output_dir / "weights" / "best.pt",  # 最佳模型
            output_dir / "weights" / "last.pt",  # 最后一个epoch的模型
            output_dir / "best.pt",              # 直接在输出目录的最佳模型
            output_dir / "last.pt"               # 直接在输出目录的最后模型
        ]
        
        # 优先返回best.pt，其次是last.pt
        for model_path in possible_model_files:
            if model_path.exists():
                print(f"✅ 找到模型文件: {model_path}")
                return str(model_path)
        
        # 如果没有找到标准命名的模型，搜索输出目录中的.pt文件
        if output_dir.exists():
            pt_files = list(output_dir.rglob("*.pt"))
            if pt_files:
                # 按修改时间排序，返回最新的
                newest_model = max(pt_files, key=lambda x: x.stat().st_mtime)
                print(f"✅ 找到最新模型文件: {newest_model}")
                return str(newest_model)
        
        print(f"⚠️  未在输出目录找到模型文件: {output_dir}")
        return None
        
    except Exception as e:
        print(f"❌ 查找模型文件时出错: {str(e)}")
        return None


async def cleanup_process_pool():
    """清理进程池资源"""
    global process_pool
    if process_pool:
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, lambda: process_pool.shutdown(wait=True))
        process_pool = None