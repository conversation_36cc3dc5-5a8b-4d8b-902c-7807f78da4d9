"""
模型预测服务
支持使用训练好的模型对图片进行预测，返回结构化数据和预测后的图片
"""

import os
import io
import base64
import logging
import hashlib
import requests
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import numpy as np

logger = logging.getLogger(__name__)


class ModelPredictionService:
    """模型预测服务"""
    
    def __init__(self):
        self.loaded_models: Dict[str, Any] = {}  # 缓存已加载的模型，key为hash值
        self.model_cache_limit = 5  # 最多缓存5个模型
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.confidence_threshold = 0.25  # 默认置信度阈值
        self.iou_threshold = 0.45  # 默认IoU阈值
        self.model_cache_dir = Path("cache/models")  # 模型缓存目录
        self.model_cache_dir.mkdir(parents=True, exist_ok=True)
        
    async def predict_image(
        self,
        model_path: Optional[str] = None,
        model_url: Optional[str] = None,
        model_hash: Optional[str] = None,
        image_data: bytes = None,
        confidence: Optional[float] = None,
        iou_threshold: Optional[float] = None,
        image_size: Optional[int] = None,
        save_visualization: bool = True
    ) -> Dict[str, Any]:
        """
        对图片进行预测
        
        Args:
            model_path: 本地模型文件路径
            model_url: 模型下载地址
            model_hash: 模型hash值，用于缓存
            image_data: 图片数据（字节）
            confidence: 置信度阈值
            iou_threshold: IoU阈值
            image_size: 推理图片大小
            save_visualization: 是否保存可视化结果
            
        Returns:
            Dict: 包含预测结果和可视化图片的字典
        """
        try:
            # 获取或下载模型
            model, actual_model_path = await self._get_or_download_model(
                model_path=model_path,
                model_url=model_url, 
                model_hash=model_hash
            )
            
            # 处理图片
            image = self._process_image_data(image_data)
            
            # 设置预测参数
            conf = confidence or self.confidence_threshold
            iou = iou_threshold or self.iou_threshold
            imgsz = image_size or 640
            
            logger.info(f"开始预测: model={actual_model_path}, conf={conf}, iou={iou}, imgsz={imgsz}")
            
            # 执行预测
            results = model.predict(
                source=image,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                save=False,
                verbose=False
            )
            
            # 解析预测结果
            prediction_data = self._parse_prediction_results(results)
            
            # 生成可视化图片
            visualization_data = None
            if save_visualization and prediction_data['detections']:
                visualization_data = await self._create_visualization(
                    image, results, prediction_data
                )
            
            return {
                "success": True,
                "model_path": actual_model_path,
                "model_hash": model_hash,
                "prediction_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "parameters": {
                    "confidence": conf,
                    "iou_threshold": iou,
                    "image_size": imgsz
                },
                "predictions": prediction_data,
                "visualization": visualization_data,
                "statistics": {
                    "total_detections": len(prediction_data['detections']),
                    "classes_detected": len(set(det['class_name'] for det in prediction_data['detections'])),
                    "avg_confidence": np.mean([det['confidence'] for det in prediction_data['detections']]) if prediction_data['detections'] else 0.0
                }
            }
            
        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "model_path": model_path or model_url,
                "model_hash": model_hash,
                "prediction_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
    async def _get_or_download_model(self, model_path: Optional[str], model_url: Optional[str], model_hash: Optional[str]) -> tuple:
        """
        获取或下载模型
        
        Args:
            model_path: 本地模型路径
            model_url: 模型下载URL
            model_hash: 模型hash值
            
        Returns:
            tuple: (model对象, 实际模型路径)
        """
        try:
            # 如果提供了本地路径，直接加载
            if model_path:
                if not os.path.exists(model_path):
                    raise FileNotFoundError(f"模型文件不存在: {model_path}")
                
                # 计算文件hash作为缓存键
                file_hash = model_hash or await self._calculate_file_hash(model_path)
                
                # 检查是否已缓存
                if file_hash in self.loaded_models:
                    logger.debug(f"使用缓存的模型: {model_path} (hash: {file_hash})")
                    return self.loaded_models[file_hash], model_path
                
                # 加载模型
                model = await self._load_model_from_path(model_path)
                self._cache_model(file_hash, model)
                return model, model_path
                
            # 如果提供了URL，下载模型
            elif model_url:
                if not model_hash:
                    # 如果没有提供hash，先下载文件计算hash
                    temp_path = await self._download_model_temp(model_url)
                    model_hash = await self._calculate_file_hash(temp_path)
                    os.remove(temp_path)  # 删除临时文件
                
                # 检查hash是否已缓存
                if model_hash in self.loaded_models:
                    logger.debug(f"使用缓存的模型: URL={model_url} (hash: {model_hash})")
                    # 返回缓存的模型，但实际路径可能不同
                    cached_model = self.loaded_models[model_hash]
                    cache_path = self.model_cache_dir / f"{model_hash}.pt"
                    return cached_model, str(cache_path)
                
                # 下载并缓存模型
                downloaded_path = await self._download_model(model_url, model_hash)
                model = await self._load_model_from_path(downloaded_path)
                self._cache_model(model_hash, model)
                
                return model, downloaded_path
            
            else:
                raise ValueError("必须提供model_path或model_url")
                
        except Exception as e:
            logger.error(f"获取模型失败: {str(e)}")
            raise
    
    async def _load_model(self, model_path: str):
        """加载模型，支持缓存"""
        try:
            # 检查是否已缓存
            if model_path in self.loaded_models:
                logger.debug(f"使用缓存的模型: {model_path}")
                return self.loaded_models[model_path]
            
            # 清理缓存（如果超过限制）
            if len(self.loaded_models) >= self.model_cache_limit:
                # 移除最老的模型
                oldest_model = next(iter(self.loaded_models))
                del self.loaded_models[oldest_model]
                logger.info(f"清理模型缓存: {oldest_model}")
            
            # 加载新模型
            logger.info(f"加载模型: {model_path}")
            from ultralytics import YOLO
            model = YOLO(model_path)
            
            # 缓存模型
            self.loaded_models[model_path] = model
            logger.info(f"模型加载完成并缓存: {model_path}")
            
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def _process_image_data(self, image_data: bytes) -> Image.Image:
        """处理图片数据"""
        try:
            # 从字节数据创建PIL图片
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为RGB（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')
                
            logger.debug(f"图片处理完成: 大小={image.size}, 模式={image.mode}")
            return image
            
        except Exception as e:
            logger.error(f"图片处理失败: {str(e)}")
            raise ValueError(f"无效的图片数据: {str(e)}")
    
    def _parse_prediction_results(self, results) -> Dict[str, Any]:
        """解析预测结果为结构化数据"""
        try:
            detections = []
            
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes
                    
                    for i in range(len(boxes)):
                        # 获取边界框坐标
                        box = boxes.xyxy[i].cpu().numpy()
                        x1, y1, x2, y2 = box
                        
                        # 获取置信度
                        confidence = float(boxes.conf[i].cpu().numpy())
                        
                        # 获取类别
                        class_id = int(boxes.cls[i].cpu().numpy())
                        class_name = result.names[class_id]
                        
                        detection = {
                            "class_id": class_id,
                            "class_name": class_name,
                            "confidence": confidence,
                            "bbox": {
                                "x1": float(x1),
                                "y1": float(y1),
                                "x2": float(x2),
                                "y2": float(y2),
                                "width": float(x2 - x1),
                                "height": float(y2 - y1),
                                "center_x": float((x1 + x2) / 2),
                                "center_y": float((y1 + y2) / 2)
                            }
                        }
                        detections.append(detection)
            
            # 按置信度排序
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            
            return {
                "detections": detections,
                "image_shape": {
                    "height": results[0].orig_shape[0] if results else 0,
                    "width": results[0].orig_shape[1] if results else 0
                }
            }
            
        except Exception as e:
            logger.error(f"结果解析失败: {str(e)}")
            return {"detections": [], "image_shape": {"height": 0, "width": 0}}
    
    async def _create_visualization(self, original_image: Image.Image, results, prediction_data: Dict) -> Dict[str, Any]:
        """创建预测结果的可视化图片"""
        try:
            # 复制原图
            vis_image = original_image.copy()
            draw = ImageDraw.Draw(vis_image)
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            # 定义颜色
            colors = [
                '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
                '#FFA500', '#800080', '#FFC0CB', '#A52A2A', '#808080', '#000080'
            ]
            
            # 绘制检测框
            for i, detection in enumerate(prediction_data['detections']):
                bbox = detection['bbox']
                class_name = detection['class_name']
                confidence = detection['confidence']
                
                # 选择颜色
                color = colors[detection['class_id'] % len(colors)]
                
                # 绘制边界框
                draw.rectangle(
                    [(bbox['x1'], bbox['y1']), (bbox['x2'], bbox['y2'])],
                    outline=color,
                    width=2
                )
                
                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                
                # 获取文本大小
                bbox_text = draw.textbbox((0, 0), label, font=font)
                text_width = bbox_text[2] - bbox_text[0]
                text_height = bbox_text[3] - bbox_text[1]
                
                # 绘制标签背景
                draw.rectangle(
                    [(bbox['x1'], bbox['y1'] - text_height - 4), 
                     (bbox['x1'] + text_width + 4, bbox['y1'])],
                    fill=color
                )
                
                # 绘制标签文本
                draw.text(
                    (bbox['x1'] + 2, bbox['y1'] - text_height - 2),
                    label,
                    fill='white',
                    font=font
                )
            
            # 转换为Base64
            img_buffer = io.BytesIO()
            vis_image.save(img_buffer, format='PNG')
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
            
            return {
                "format": "base64",
                "content": img_base64,
                "content_type": "image/png",
                "size": {
                    "width": vis_image.width,
                    "height": vis_image.height
                }
            }
            
        except Exception as e:
            logger.error(f"可视化创建失败: {str(e)}")
            return None
    
    async def batch_predict(
        self,
        model_path: Optional[str] = None,
        model_url: Optional[str] = None,
        model_hash: Optional[str] = None,
        images_data: List[bytes] = None,
        confidence: Optional[float] = None,
        iou_threshold: Optional[float] = None,
        image_size: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """批量预测多张图片"""
        results = []
        
        for i, image_data in enumerate(images_data):
            logger.info(f"批量预测进度: {i+1}/{len(images_data)}")
            
            result = await self.predict_image(
                model_path=model_path,
                model_url=model_url,
                model_hash=model_hash,
                image_data=image_data,
                confidence=confidence,
                iou_threshold=iou_threshold,
                image_size=image_size,
                save_visualization=True
            )
            
            result['batch_index'] = i
            results.append(result)
        
        return results
    
    def get_model_info(self, model_path: str) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            if not os.path.exists(model_path):
                return {"error": "模型文件不存在"}
            
            model_stat = os.stat(model_path)
            
            info = {
                "model_path": model_path,
                "file_size": model_stat.st_size,
                "created_time": datetime.fromtimestamp(model_stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(model_stat.st_mtime).isoformat(),
                "is_cached": model_path in self.loaded_models
            }
            
            # 如果模型已加载，获取更多信息
            if model_path in self.loaded_models:
                try:
                    model = self.loaded_models[model_path]
                    info.update({
                        "model_type": type(model).__name__,
                        "class_names": getattr(model, 'names', {}),
                        "num_classes": len(getattr(model, 'names', {}))
                    })
                except:
                    pass
            
            return info
            
        except Exception as e:
            return {"error": str(e)}
    
    def clear_model_cache(self, model_path: Optional[str] = None):
        """清理模型缓存"""
        if model_path:
            if model_path in self.loaded_models:
                del self.loaded_models[model_path]
                logger.info(f"已清理模型缓存: {model_path}")
        else:
            self.loaded_models.clear()
            logger.info("已清理所有模型缓存")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "cached_models": list(self.loaded_models.keys()),
            "cache_count": len(self.loaded_models),
            "cache_limit": self.model_cache_limit,
            "supported_formats": self.supported_formats,
            "default_confidence": self.confidence_threshold,
            "default_iou": self.iou_threshold
        }
    
    async def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件hash值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件hash失败 {file_path}: {str(e)}")
            raise
    
    async def _load_model_from_path(self, model_path: str):
        """从路径加载模型"""
        try:
            logger.info(f"加载模型: {model_path}")
            from ultralytics import YOLO
            model = YOLO(model_path)
            logger.info(f"模型加载完成: {model_path}")
            return model
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def _cache_model(self, model_hash: str, model):
        """缓存模型"""
        # 清理缓存（如果超过限制）
        if len(self.loaded_models) >= self.model_cache_limit:
            # 移除最老的模型
            oldest_hash = next(iter(self.loaded_models))
            del self.loaded_models[oldest_hash]
            logger.info(f"清理模型缓存: {oldest_hash}")
        
        # 缓存新模型
        self.loaded_models[model_hash] = model
        logger.info(f"模型已缓存: hash={model_hash}")
    
    async def _download_model_temp(self, url: str) -> str:
        """临时下载模型用于计算hash"""
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pt')
        try:
            logger.info(f"临时下载模型: {url}")
            response = requests.get(url, stream=True, timeout=300)
            response.raise_for_status()
            
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            
            temp_file.close()
            logger.info(f"临时下载完成: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            temp_file.close()
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)
            logger.error(f"临时下载失败: {str(e)}")
            raise
    
    async def _download_model(self, url: str, model_hash: str) -> str:
        """下载模型到缓存目录"""
        cache_path = self.model_cache_dir / f"{model_hash}.pt"
        
        # 如果文件已存在且hash匹配，直接返回
        if cache_path.exists():
            existing_hash = await self._calculate_file_hash(str(cache_path))
            if existing_hash == model_hash:
                logger.info(f"模型文件已存在: {cache_path}")
                return str(cache_path)
            else:
                logger.warning(f"缓存文件hash不匹配，重新下载: {cache_path}")
                os.remove(cache_path)
        
        try:
            logger.info(f"下载模型: {url} -> {cache_path}")
            response = requests.get(url, stream=True, timeout=300)
            response.raise_for_status()
            
            # 检查文件大小
            file_size = int(response.headers.get('content-length', 0))
            if file_size > 500 * 1024 * 1024:  # 500MB限制
                raise ValueError(f"模型文件过大: {file_size / 1024 / 1024:.1f}MB")
            
            with open(cache_path, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    downloaded += len(chunk)
                    if file_size > 0:
                        progress = downloaded / file_size * 100
                        if downloaded % (1024 * 1024) == 0:  # 每1MB打印一次进度
                            logger.info(f"下载进度: {progress:.1f}% ({downloaded / 1024 / 1024:.1f}MB)")
            
            # 验证下载的文件hash
            downloaded_hash = await self._calculate_file_hash(str(cache_path))
            if model_hash and downloaded_hash != model_hash:
                os.remove(cache_path)
                raise ValueError(f"下载文件hash不匹配: 期望={model_hash}, 实际={downloaded_hash}")
            
            logger.info(f"模型下载完成: {cache_path}")
            return str(cache_path)
            
        except Exception as e:
            if cache_path.exists():
                os.remove(cache_path)
            logger.error(f"下载模型失败: {str(e)}")
            raise


# 全局预测服务实例
model_prediction_service = ModelPredictionService()