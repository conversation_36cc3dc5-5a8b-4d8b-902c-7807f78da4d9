import psutil
import asyncio
from typing import Optional, Dict
from app.models.schemas import ResourceUsage
from app.models.enums import TaskStatus
from app.storage.memory import task_storage

try:
    import pynvml
    NVML_AVAILABLE = True
    pynvml.nvmlInit()
except ImportError:
    NVML_AVAILABLE = False


class ResourceMonitor:
    def __init__(self):
        self.monitoring_tasks: Dict[int, asyncio.Task] = {}
    
    def get_current_usage(self) -> ResourceUsage:
        """获取当前系统资源使用情况"""
        usage = ResourceUsage()
        
        # CPU和内存使用率（非阻塞方式）
        usage.cpu_percent = psutil.cpu_percent(interval=None)  # 非阻塞获取
        usage.memory_percent = psutil.virtual_memory().percent
        
        # GPU使用情况
        if NVML_AVAILABLE:
            try:
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)  # 使用第一个GPU
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                
                usage.gpu_memory_used = memory_info.used / 1024 / 1024  # MB
                usage.gpu_memory_total = memory_info.total / 1024 / 1024  # MB
                usage.gpu_utilization = utilization.gpu
            except Exception:
                pass
        
        return usage
    
    async def monitor_task(self, task_id: int, interval: int = 10):
        """监控特定任务的资源使用"""
        while True:
            task = task_storage.get(task_id)
            if not task or task.status not in [TaskStatus.RUNNING, TaskStatus.PENDING]:
                break
            
            # 更新任务的资源使用情况
            task.resource_usage = self.get_current_usage()
            
            await asyncio.sleep(interval)
        
        # 清理监控任务
        if task_id in self.monitoring_tasks:
            del self.monitoring_tasks[task_id]
    
    def start_monitoring(self, task_id: int):
        """开始监控任务"""
        if task_id not in self.monitoring_tasks:
            task = asyncio.create_task(self.monitor_task(task_id))
            self.monitoring_tasks[task_id] = task
    
    def stop_monitoring(self, task_id: int):
        """停止监控任务"""
        if task_id in self.monitoring_tasks:
            self.monitoring_tasks[task_id].cancel()
            del self.monitoring_tasks[task_id]


resource_monitor = ResourceMonitor()