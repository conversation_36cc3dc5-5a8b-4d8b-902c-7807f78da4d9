import os
import socket
import asyncio
import threading
import subprocess
from pathlib import Path
from typing import Optional

from app.config.settings import swanlab_settings


class LogDashboardService:
    """日志面板服务管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.thread: Optional[threading.Thread] = None
        self.host = "127.0.0.1"
        self.port: Optional[int] = None
        self.log_path = swanlab_settings.logdir
        self.is_running = False
        self._stop_event = threading.Event()
    
    def get_free_port(self, default_port: int = 5092) -> int:
        """获取一个可用端口"""
        # 判断默认端口是否可用
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind((self.host, default_port))
                return default_port
            except OSError:
                pass
        
        # 如果默认端口被占用，返回一个随机可用端口
        sock = socket.socket()
        sock.bind((self.host, 0))
        _, port = sock.getsockname()
        sock.close()
        return port
    
    async def start_dashboard(self, log_path: Optional[str] = None, host: str = "0.0.0.0", port: Optional[int] = None) -> dict:
        """启动日志面板服务"""
        try:
            if self.is_running:
                return {
                    "success": True,
                    "message": "日志面板已经在运行",
                    "url": f"http://{self.host}:{self.port}",
                    "port": self.port
                }
            
            # 设置参数
            self.host = host
            self.port = port or self.get_free_port()
            
            # 设置日志路径
            if log_path:
                self.log_path = log_path
            else:
                self.log_path = os.path.abspath(swanlab_settings.logdir)
            
            # 确保日志目录存在
            Path(self.log_path).mkdir(parents=True, exist_ok=True)
            
            print(f"🚀 启动日志面板服务...")
            print(f"   主机: {self.host}")
            print(f"   端口: {self.port}")
            print(f"   日志路径: {self.log_path}")
            
            # 检查swanboard是否已安装
            try:
                from swanboard import SwanBoardRun
            except ImportError:
                return {
                    "success": False,
                    "message": "未安装swanboard包，请运行: pip install swanlab[dashboard]",
                    "error": "ModuleNotFoundError"
                }
            
            # 在独立线程中启动面板服务
            self.thread = threading.Thread(
                target=self._start_dashboard_process,
                name=f"SwanBoard-{self.port}",
                daemon=True
            )
            self.thread.start()
            
            # 等待服务启动
            await asyncio.sleep(2)
            
            # 验证服务是否启动成功
            if await self._check_dashboard_health():
                self.is_running = True
                dashboard_url = f"http://127.0.0.1:{self.port}"
                print(f"✅ 日志面板服务启动成功: {dashboard_url}")
                
                return {
                    "success": True,
                    "message": "日志面板服务启动成功",
                    "url": dashboard_url,
                    "port": self.port,
                    "log_path": self.log_path
                }
            else:
                return {
                    "success": False,
                    "message": "日志面板服务启动失败，无法连接到服务",
                    "error": "ServiceStartupFailed"
                }
                
        except Exception as e:
            print(f"❌ 启动日志面板服务失败: {e}")
            return {
                "success": False,
                "message": f"启动日志面板服务失败: {str(e)}",
                "error": type(e).__name__
            }
    
    def _start_dashboard_process(self):
        """在独立线程中启动面板服务"""
        try:
            from swanboard import SwanBoardRun
            from swanlab.env import SwanLabEnv
            # 验证主机和端口
            SwanBoardRun.is_valid_port(self.port)
            SwanBoardRun.is_valid_ip(self.host)
            
            print(f"🎛️ 在独立线程中启动SwanBoard服务...")
            print(f"   路径: {self.log_path}")
            print(f"   地址: {self.host}:{self.port}")
            os.environ[SwanLabEnv.SWANLOG_FOLDER.value] = self.log_path
            # 启动服务（这将阻塞当前线程）
            SwanBoardRun.run(
                path=self.log_path,
                host=self.host,
                port=self.port,
            )
            
        except Exception as e:
            print(f"❌ 日志面板线程启动失败: {e}")
            self.is_running = False
    
    async def _check_dashboard_health(self) -> bool:
        """检查面板服务健康状态"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"http://127.0.0.1:{self.port}"
                async with session.get(url, timeout=5) as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def stop_dashboard(self) -> dict:
        """停止日志面板服务"""
        try:
            if not self.is_running:
                return {
                    "success": True,
                    "message": "日志面板服务未运行"
                }
            
            print("🛑 停止日志面板服务...")
            
            # 设置停止事件
            self._stop_event.set()
            
            # 尝试通过端口kill服务进程
            if self.port:
                try:
                    import psutil
                    for proc in psutil.process_iter(['pid', 'name', 'connections']):
                        try:
                            # 检查进程是否监听我们的端口
                            for conn in proc.connections():
                                if conn.laddr.port == self.port and conn.status == 'LISTEN':
                                    print(f"🔪 终止监听端口{self.port}的进程 PID: {proc.pid}")
                                    proc.terminate()
                                    try:
                                        proc.wait(timeout=3)
                                    except psutil.TimeoutExpired:
                                        proc.kill()
                                    break
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                except ImportError:
                    print("⚠️ psutil未安装，无法精确终止进程")
            
            # 等待线程结束
            if self.thread and self.thread.is_alive():
                print("⏳ 等待面板线程结束...")
                self.thread.join(timeout=3)
                if self.thread.is_alive():
                    print("⚠️ 面板线程未能在3秒内结束")
            
            # 如果有进程在运行，尝试终止
            if self.process and self.process.poll() is None:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
            
            self.is_running = False
            self.process = None
            self.thread = None
            self.port = None
            
            print("✅ 日志面板服务已停止")
            
            return {
                "success": True,
                "message": "日志面板服务已停止"
            }
            
        except Exception as e:
            print(f"❌ 停止日志面板服务失败: {e}")
            return {
                "success": False,
                "message": f"停止日志面板服务失败: {str(e)}",
                "error": type(e).__name__
            }
    
    def get_status(self) -> dict:
        """获取面板服务状态"""
        return {
            "is_running": self.is_running,
            "host": self.host if self.is_running else None,
            "port": self.port if self.is_running else None,
            "url": f"http://{self.host}:{self.port}" if self.is_running else None,
            "log_path": self.log_path
        }
    
    async def restart_dashboard(self) -> dict:
        """重启日志面板服务"""
        print("🔄 重启日志面板服务...")
        
        # 先停止
        stop_result = await self.stop_dashboard()
        if not stop_result["success"]:
            return stop_result
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 再启动
        return await self.start_dashboard()


# 全局日志面板服务实例
log_dashboard_service = LogDashboardService()