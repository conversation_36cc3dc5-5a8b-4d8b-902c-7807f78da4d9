"""
外部训练服务客户端
用于与 hyai-train-biz 服务进行状态同步
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import httpx
from httpx import AsyncClient, ConnectTimeout, ReadTimeout, RequestError

from app.config.settings import external_service_settings
from app.models.schemas import TaskStatusChangeRequestDTO, TaskStatusChangeResponseDTO
from app.models.enums import TaskStatus

logger = logging.getLogger(__name__)


class ExternalServiceClient:
    """外部训练服务客户端"""
    
    def __init__(self):
        self.default_base_url = external_service_settings.base_url
        self.base_url = self.default_base_url
        self.status_change_url = f"{self.base_url}{external_service_settings.status_change_endpoint}"
        self.health_url = f"{self.base_url}{external_service_settings.health_endpoint}"
        self.timeout = external_service_settings.timeout
        self.retry_attempts = external_service_settings.retry_attempts
        self.retry_delay = external_service_settings.retry_delay
        self.enabled = external_service_settings.enable_notifications
        
    async def send_status_change(
        self, 
        external_task_id: int, 
        status: str,
        progress: Optional[Any] = None,
        current_metrics: Optional[Any] = None,
        best_metrics: Optional[Any] = None,
        resource_usage: Optional[Any] = None,
        error_message: Optional[str] = None,
        output_path: Optional[str] = None,
        output_model_path: Optional[str] = None,  # 向后兼容
        warnings: Optional[List[str]] = None,
        log_file: Optional[str] = None,
        checkpoints: Optional[List[Any]] = None,
        best_checkpoint: Optional[str] = None,
        model_delivery: Optional[Any] = None,  # 模型交付信息
        timestamp: Optional[datetime] = None,
        start_time: Optional[datetime] = None,  # 向后兼容
        end_time: Optional[datetime] = None,    # 向后兼容
    ) -> Optional[TaskStatusChangeResponseDTO]:
        """
        发送任务状态变更通知到外部服务
        
        Args:
            external_task_id: 外部任务ID
            status: 任务状态
            progress: 训练进度信息
            error_message: 错误信息
            output_model_path: 输出模型路径
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            TaskStatusChangeResponseDTO: 响应结果，失败时返回None
        """
        if not self.enabled:
            logger.info(f"外部服务通知已禁用，跳过状态变更通知: task_id={external_task_id}, status={status}")
            return None
            
        # 处理向后兼容性参数
        final_output_path = output_path or output_model_path
        final_timestamp = timestamp or start_time or end_time
        
        # 构建请求数据（匹配外部服务期望的完整格式）
        request_data = TaskStatusChangeRequestDTO(
            external_task_id=external_task_id,
            status=status,
            progress=progress,
            current_metrics=current_metrics,
            best_metrics=best_metrics,
            resource_usage=resource_usage,
            error_message=error_message,
            output_path=final_output_path,
            warnings=warnings,
            log_file=log_file,
            checkpoints=checkpoints,
            best_checkpoint=best_checkpoint,
            model_delivery=model_delivery,
            timestamp=final_timestamp.strftime("%Y-%m-%d %H:%M:%S") if final_timestamp else None
        )
        
        # 重试机制
        for attempt in range(self.retry_attempts):
            try:
                logger.info(f"发送状态变更通知 (尝试 {attempt + 1}/{self.retry_attempts}): "
                           f"external_task_id={external_task_id}, status={status}")
                
                async with AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        self.status_change_url,
                        json=request_data.model_dump(exclude_none=True, by_alias=True),
                        headers={"Content-Type": "application/json"}
                    )
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        result = TaskStatusChangeResponseDTO(**response_data)
                        
                        if result.code == 0:
                            logger.info(f"状态变更通知发送成功: external_task_id={external_task_id}, "
                                      f"status={status}, response={result.msg or 'Success'}")
                            return result
                        else:
                            logger.warning(f"状态变更通知处理失败: external_task_id={external_task_id}, "
                                         f"status={status}, error={result.msg or 'Unknown error'}")
                            return result
                    else:
                        logger.warning(f"状态变更通知HTTP请求失败: external_task_id={external_task_id}, "
                                     f"status_code={response.status_code}, response={response.text}")
                        
            except (ConnectTimeout, ReadTimeout) as e:
                logger.warning(f"状态变更通知超时 (尝试 {attempt + 1}/{self.retry_attempts}): "
                             f"external_task_id={external_task_id}, error={str(e)}")
                
            except RequestError as e:
                logger.warning(f"状态变更通知请求错误 (尝试 {attempt + 1}/{self.retry_attempts}): "
                             f"external_task_id={external_task_id}, error={str(e)}")
                
            except Exception as e:
                logger.error(f"状态变更通知未知错误 (尝试 {attempt + 1}/{self.retry_attempts}): "
                           f"external_task_id={external_task_id}, error={str(e)}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.retry_attempts - 1:
                await asyncio.sleep(self.retry_delay * (attempt + 1))  # 指数退避
        
        logger.error(f"状态变更通知发送失败，已达到最大重试次数: external_task_id={external_task_id}, status={status}")
        return None
    
    async def check_health(self) -> bool:
        """
        检查外部服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        if not self.enabled:
            return True
            
        try:
            async with AsyncClient(timeout=10) as client:
                response = await client.get(self.health_url)
                is_healthy = response.status_code == 200
                
                if is_healthy:
                    logger.debug("外部服务健康检查通过")
                else:
                    logger.warning(f"外部服务健康检查失败: status_code={response.status_code}")
                    
                return is_healthy
                
        except Exception as e:
            logger.warning(f"外部服务健康检查异常: {str(e)}")
            return False
    
    def map_local_status_to_external(self, local_status: TaskStatus) -> str:
        """
        将本地任务状态映射为外部服务状态
        
        Args:
            local_status: 本地任务状态
            
        Returns:
            str: 外部服务状态
        """
        status_mapping = {
            TaskStatus.PENDING: "pending",
            TaskStatus.UPLOADING: "uploading", 
            TaskStatus.RUNNING: "running",
            TaskStatus.PAUSED: "paused",
            TaskStatus.STOPPING: "stopping",
            TaskStatus.STOPPED: "cancelled",  # 本地的stopped映射为外部的cancelled
            TaskStatus.COMPLETED: "completed",
            TaskStatus.FAILED: "failed",
            TaskStatus.CANCELLED: "cancelled"
        }
        
        return status_mapping.get(local_status, "pending")
    
    def update_host(self, new_host: str) -> bool:
        """
        更新外部服务的host地址
        
        Args:
            new_host: 新的host地址 (如: http://*************:8080/api/train)
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 确保host以http://或https://开头
            if not new_host.startswith(('http://', 'https://')):
                new_host = 'http://' + new_host
            
            # 移除末尾的斜杠
            new_host = new_host.rstrip('/')
            
            self.base_url = new_host
            self.status_change_url = f"{self.base_url}{external_service_settings.status_change_endpoint}"
            self.health_url = f"{self.base_url}{external_service_settings.health_endpoint}"
            
            logger.info(f"外部服务host已更新:")
            logger.info(f"  Base URL: {self.base_url}")
            logger.info(f"  Status Change: {self.status_change_url}")
            logger.info(f"  Health Check: {self.health_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新外部服务host失败: {e}")
            return False
    
    def reset_host(self) -> bool:
        """
        重置host为默认配置
        
        Returns:
            bool: 重置是否成功
        """
        try:
            self.base_url = self.default_base_url
            self.status_change_url = f"{self.base_url}{external_service_settings.status_change_endpoint}"
            self.health_url = f"{self.base_url}{external_service_settings.health_endpoint}"
            
            logger.info(f"外部服务host已重置为默认配置: {self.base_url}")
            return True
            
        except Exception as e:
            logger.error(f"重置外部服务host失败: {e}")
            return False
    
    def get_current_config(self) -> Dict[str, Any]:
        """
        获取当前客户端配置
        
        Returns:
            Dict[str, Any]: 当前配置信息
        """
        return {
            "base_url": self.base_url,
            "default_base_url": self.default_base_url,
            "status_change_url": self.status_change_url,
            "health_url": self.health_url,
            "timeout": self.timeout,
            "retry_attempts": self.retry_attempts,
            "retry_delay": self.retry_delay,
            "enabled": self.enabled
        }


# 全局客户端实例
external_client = ExternalServiceClient()
