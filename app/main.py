from fastapi import FastAPI
from contextlib import asynccontextmanager
from app.api.routes import health, tasks, metrics, control, system, dashboard, model_callback, prediction
from app.services.training import cleanup_process_pool
from app.services.log_dashboard import log_dashboard_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时的逻辑
    print("🚀 训练服务启动中...")
    
    # 自动启动日志面板服务
    try:
        print("🎛️ 启动日志面板服务...")
        dashboard_result = await log_dashboard_service.start_dashboard()
        if dashboard_result["success"]:
            print(f"✅ 日志面板已启动: {dashboard_result['url']}")
            print(f"   端口: {dashboard_result['port']}")
            print(f"   日志路径: {dashboard_result['log_path']}")
        else:
            print(f"⚠️ 日志面板启动失败: {dashboard_result['message']}")
    except Exception as dashboard_error:
        print(f"⚠️ 启动日志面板时出错: {dashboard_error}")
    
    print("✅ 训练服务启动完成")
    
    yield
    
    # 应用关闭时的逻辑
    print("🛑 训练服务关闭中...")
    await cleanup_process_pool()
    
    # 停止日志面板服务
    try:
        await log_dashboard_service.stop_dashboard()
        print("✅ 日志面板服务已停止")
    except Exception as e:
        print(f"⚠️ 停止日志面板服务时出错: {e}")
    
    print("✅ 训练服务关闭完成")


app = FastAPI(
    title="训练任务管理服务", 
    version="2.0.0", 
    description="企业级YOLO训练任务管理API",
    lifespan=lifespan
)

app.include_router(health.router)
app.include_router(tasks.router)
app.include_router(metrics.router)
app.include_router(control.router)
app.include_router(system.router)
app.include_router(dashboard.router)
app.include_router(model_callback.router)
app.include_router(prediction.router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)