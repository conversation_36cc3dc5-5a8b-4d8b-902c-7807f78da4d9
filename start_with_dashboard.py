#!/usr/bin/env python3
"""
启动训练服务并自动启动日志面板

这个脚本会启动训练管理服务，并在启动过程中自动启动日志面板服务。
日志面板服务会在后台运行，提供训练日志的Web界面访问。

使用方法:
python start_with_dashboard.py

或者直接运行主服务（面板会自动启动）:
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
"""

import uvicorn
import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.main import app


def handle_shutdown(signum, frame):
    """处理关闭信号"""
    print(f"\n📡 收到信号 {signum}，准备关闭服务...")
    sys.exit(0)


async def check_dashboard_status():
    """检查面板状态"""
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/dashboard/status", timeout=5) as response:
                if response.status == 200:
                    status = await response.json()
                    if status['is_running']:
                        print(f"✅ 日志面板运行状态: {status['url']}")
                        return status['url']
                    else:
                        print("⚠️ 日志面板未运行")
                        return None
                else:
                    print(f"⚠️ 无法获取面板状态: {response.status}")
                    return None
    except Exception as e:
        print(f"⚠️ 检查面板状态时出错: {e}")
        return None


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 启动训练管理服务（含日志面板）")
    print("=" * 60)
    print()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_shutdown)
    signal.signal(signal.SIGTERM, handle_shutdown)
    
    print("📋 服务信息:")
    print("   - 训练服务: http://localhost:8000")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 日志面板: 自动启动")
    print()
    
    print("💡 可用的面板管理接口:")
    print("   - GET  /dashboard/status   - 获取面板状态")
    print("   - POST /dashboard/start    - 启动面板服务")
    print("   - POST /dashboard/stop     - 停止面板服务")
    print("   - POST /dashboard/restart  - 重启面板服务")
    print("   - GET  /dashboard/url      - 获取面板访问地址")
    print()
    
    try:
        # 启动服务
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务被用户中断")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
    finally:
        print("🏁 服务已退出")


async def post_startup_check():
    """启动后检查"""
    print("\n" + "=" * 60)
    print("🔍 启动后状态检查")
    print("=" * 60)
    
    # 等待服务完全启动
    await asyncio.sleep(3)
    
    # 检查面板状态
    dashboard_url = await check_dashboard_status()
    
    if dashboard_url:
        print(f"\n🎉 服务启动完成!")
        print(f"   训练服务: http://localhost:8000")
        print(f"   日志面板: {dashboard_url}")
        print(f"   API文档: http://localhost:8000/docs")
    else:
        print("\n⚠️ 日志面板可能未正常启动，请检查日志")
    
    print("\n📝 使用提示:")
    print("   1. 创建训练任务时会自动确保面板运行")
    print("   2. 可通过 /dashboard/url 获取面板地址")
    print("   3. 面板会实时显示SwanLab训练日志")
    print("   4. 面板服务会随主服务一起关闭")


if __name__ == "__main__":
    print("🎬 启动训练管理服务...")
    
    try:
        main()
    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
        sys.exit(1)