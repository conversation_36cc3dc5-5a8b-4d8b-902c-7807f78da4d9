#!/usr/bin/env python3
"""
训练服务管理脚本
支持 start、stop、restart 操作

使用方法:
    python service_manager.py start    # 启动服务
    python service_manager.py stop     # 停止服务
    python service_manager.py restart  # 重启服务
    python service_manager.py status   # 查看服务状态
"""

import os
import sys
import time
import signal
import psutil
import argparse
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

# 配置
SERVICE_NAME = "train_manager"
PID_FILE = Path("./logs/service.pid")
LOG_FILE = Path("./logs/service.log")
HOST = "0.0.0.0"
PORT = 8000
PYTHON_MODULE = "app.main:app"

# 确保日志目录存在
LOG_FILE.parent.mkdir(exist_ok=True)


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        
    def get_service_pid(self) -> Optional[int]:
        """获取服务进程ID"""
        if not PID_FILE.exists():
            return None
            
        try:
            with open(PID_FILE, 'r') as f:
                pid = int(f.read().strip())
                
            # 检查进程是否存在
            if psutil.pid_exists(pid):
                proc = psutil.Process(pid)
                # 检查是否是我们的服务进程
                if any(PYTHON_MODULE in cmd for cmd in proc.cmdline()):
                    return pid
                    
        except (ValueError, psutil.NoSuchProcess, psutil.AccessDenied):
            pass
            
        # PID文件存在但进程不存在，清理PID文件
        if PID_FILE.exists():
            PID_FILE.unlink()
            
        return None
    
    def save_pid(self, pid: int):
        """保存进程ID到文件"""
        with open(PID_FILE, 'w') as f:
            f.write(str(pid))
    
    def remove_pid_file(self):
        """删除PID文件"""
        if PID_FILE.exists():
            PID_FILE.unlink()
    
    def is_service_running(self) -> bool:
        """检查服务是否运行中"""
        return self.get_service_pid() is not None
    
    def start_service(self) -> Dict[str, Any]:
        """启动服务"""
        if self.is_service_running():
            pid = self.get_service_pid()
            return {
                "success": False,
                "message": f"服务已在运行中 (PID: {pid})"
            }
        
        print(f"🚀 启动 {SERVICE_NAME} 服务...")
        
        try:
            # 构建启动命令
            cmd = [
                sys.executable, "-m", "uvicorn",
                PYTHON_MODULE,
                "--host", HOST,
                "--port", str(PORT),
                "--log-level", "info"
            ]
            
            # 启动服务进程
            with open(LOG_FILE, 'a') as log_file:
                log_file.write(f"\n{'='*50}\n")
                log_file.write(f"服务启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                log_file.write(f"启动命令: {' '.join(cmd)}\n")
                log_file.write(f"{'='*50}\n")
                
                process = subprocess.Popen(
                    cmd,
                    cwd=self.project_root,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    start_new_session=True
                )
            
            # 保存PID
            self.save_pid(process.pid)
            
            # 等待服务启动
            print("⏳ 等待服务启动...")
            time.sleep(3)
            
            # 检查服务是否成功启动
            if self.is_service_running():
                print(f"✅ 服务启动成功!")
                print(f"   PID: {process.pid}")
                print(f"   地址: http://{HOST}:{PORT}")
                print(f"   API文档: http://{HOST}:{PORT}/docs")
                print(f"   日志文件: {LOG_FILE}")
                
                return {
                    "success": True,
                    "message": "服务启动成功",
                    "pid": process.pid,
                    "url": f"http://{HOST}:{PORT}"
                }
            else:
                self.remove_pid_file()
                return {
                    "success": False,
                    "message": "服务启动失败，请检查日志文件"
                }
                
        except Exception as e:
            self.remove_pid_file()
            return {
                "success": False,
                "message": f"启动服务时出错: {e}"
            }
    
    def stop_service(self) -> Dict[str, Any]:
        """停止服务"""
        pid = self.get_service_pid()
        if not pid:
            return {
                "success": False,
                "message": "服务未运行"
            }
        
        print(f"🛑 停止 {SERVICE_NAME} 服务 (PID: {pid})...")
        
        try:
            process = psutil.Process(pid)
            
            # 优雅关闭
            print("⏳ 发送SIGTERM信号...")
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                print("✅ 服务已优雅停止")
            except psutil.TimeoutExpired:
                print("⚠️ 优雅停止超时，强制终止...")
                process.kill()
                process.wait(timeout=5)
                print("✅ 服务已强制停止")
            
            self.remove_pid_file()
            
            # 记录停止日志
            with open(LOG_FILE, 'a') as log_file:
                log_file.write(f"\n服务停止时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            return {
                "success": True,
                "message": "服务已停止"
            }
            
        except psutil.NoSuchProcess:
            self.remove_pid_file()
            return {
                "success": True,
                "message": "服务进程不存在，已清理PID文件"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"停止服务时出错: {e}"
            }
    
    def restart_service(self) -> Dict[str, Any]:
        """重启服务"""
        print(f"🔄 重启 {SERVICE_NAME} 服务...")
        
        # 先停止服务
        stop_result = self.stop_service()
        if not stop_result["success"] and "未运行" not in stop_result["message"]:
            return stop_result
        
        # 等待一下确保完全停止
        time.sleep(2)
        
        # 启动服务
        return self.start_service()
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        pid = self.get_service_pid()
        
        if not pid:
            return {
                "running": False,
                "message": "服务未运行"
            }
        
        try:
            process = psutil.Process(pid)
            create_time = time.strftime('%Y-%m-%d %H:%M:%S', 
                                     time.localtime(process.create_time()))
            
            # 获取内存和CPU使用情况
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            return {
                "running": True,
                "pid": pid,
                "create_time": create_time,
                "memory_mb": round(memory_info.rss / 1024 / 1024, 2),
                "cpu_percent": cpu_percent,
                "url": f"http://{HOST}:{PORT}",
                "message": "服务运行中"
            }
            
        except psutil.NoSuchProcess:
            self.remove_pid_file()
            return {
                "running": False,
                "message": "服务进程不存在"
            }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="训练服务管理器")
    parser.add_argument("action", choices=["start", "stop", "restart", "status"],
                       help="要执行的操作")
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    print(f"📋 {SERVICE_NAME} 服务管理器")
    print("=" * 50)
    
    if args.action == "start":
        result = manager.start_service()
    elif args.action == "stop":
        result = manager.stop_service()
    elif args.action == "restart":
        result = manager.restart_service()
    elif args.action == "status":
        result = manager.get_service_status()
        
        if result["running"]:
            print(f"✅ 服务状态: {result['message']}")
            print(f"   PID: {result['pid']}")
            print(f"   启动时间: {result['create_time']}")
            print(f"   内存使用: {result['memory_mb']} MB")
            print(f"   CPU使用: {result['cpu_percent']}%")
            print(f"   访问地址: {result['url']}")
            print(f"   API文档: {result['url']}/docs")
        else:
            print(f"❌ 服务状态: {result['message']}")
        
        return
    
    # 输出结果
    if result["success"]:
        print(f"✅ {result['message']}")
    else:
        print(f"❌ {result['message']}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
